import { defineComponent } from 'vue';

import { Button, Icon, showToast, Toast } from 'vant';
import { FunctionalComponent } from 'vue';

import { onMounted } from 'vue';
import { getLoginUrl } from '#src/utils/auth';

const allowedDomains = [location.origin]; 
const validateUrl = (url) => {
  try {
    const parsedUrl = new URL(url);
    // 确保 URL 的域名在白名单内
    return allowedDomains.includes(parsedUrl.origin);
  } catch {
    return false; // 如果 URL 无效，直接拒绝
  }
};

const AccountNotExist: FunctionalComponent<{ message: string }> = (props) => {
  onMounted(() => {
  });
  const onRelogin = () => {
    const redirectUrl = location.origin + location.pathname
     // 验证 redirectUrl 是否合法
     if (!validateUrl(redirectUrl)) {
      showToast(`非法的重定向 URL:${redirectUrl}`);
      return;
    }
    // debugger
    const url = getLoginUrl();
    // const url = `${import.meta.env.ONEPDT_PROXY_URL ?? ''}/api/auth/login?signonurl=${encodeURIComponent(redirectUrl)}&errorurl=${encodeURIComponent(redirectUrl+'/#/error')}`;
       // 再次验证最终生成的 URL
      //  if(true){
       if (validateUrl(url)) {
        window.location.replace(url);
        // location.href = url;
      } else {
        showToast(`非法的重定向 URL:${redirectUrl}`);
      }
  };
  return () => {
    return (
      <div class="flex flex-col  items-center mt-[20vh] flex-1 gap-4 px-2 text-center">
        <Icon name="close" color='red' size={64} />
        <div class="text-xl ">{props.message}</div>
        <Button type="primary" onClick={onRelogin}>
          重新登录{' '}
        </Button>
      </div>
    );
  };
};

export default defineComponent(AccountNotExist, { props: ['message'] });
