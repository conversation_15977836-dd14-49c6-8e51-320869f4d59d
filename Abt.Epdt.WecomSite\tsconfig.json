{
  "files": [],
  // "include": [
  //   // ** : 任意目录 ， * : 任意文件
  //   "src/**/*.ts",
  //   "src/**/*.tsx",
  //   "src/**/*.json"
  // ],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.node.json"
    }
  ],
  // "paths": {
  //   "#/*": [
  //     "src/*"
  //   ],
  //   "#components/*": [
  //     "src/components/*"
  //   ]
  // },
  "compilerOptions": {
    "strict": false,
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "noImplicitAny": false,
    "types": [
      "vue/jsx"
    ]
  }
}