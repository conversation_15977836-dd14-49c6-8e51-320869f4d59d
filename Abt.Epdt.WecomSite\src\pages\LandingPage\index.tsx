import { defineComponent, FunctionalComponent, onMounted, ref, watch } from 'vue';
import { Image, showToast } from 'vant';
import request from '#src/service/request';
import { useRoute, useRouter } from 'vue-router';
import Header from '#src/components/Header/index.jsx';
import { storeToRefs } from 'pinia';
import { useUserStore } from '#src/stores/user.js';

type AppConfig = {
  text: string;
  value: string;
  configValue: string;
};

const LandingPage: FunctionalComponent = () => {
  const list = ref<AppConfig[]>([]);
  const router = useRouter();

  const { userInfo } = storeToRefs(useUserStore());
  const getApps = async () => {
    if (!userInfo.value?.isLogin) return;
    try {
      const res = await request({ url: '/api/epdt/GetOnepdtConfig?tablename=落地页&fieldname=落地页' });
      list.value = res.data;
    } catch (error) {
      showToast('获取应用错误');
    }
  };

  watch(
    () => userInfo.value?.isLogin,
    (newValue) => {
      if (newValue === true) {
        getApps();
      }
    }
  );

  onMounted(() => {
    getApps();
  });
  return () => {
    return (
      <div>
        <Header isShowBack={false}></Header>
        <div class="grid grid-cols-3 gap-4 p-4">
          {list.value.map((app) => (
            <div
              class="flex flex-col items-center justify-center"
              onClick={() => {
                location.href = app.configValue;
              }}
            >
              <Image src={`/api/epdt/GetConfigFile?id=${app.value}`} width={64} height={64}></Image>
              <div>{app.text}</div>
            </div>
          ))}
          {/* <div class="bg-blue-500 p-4 rounded-lg flex flex-col items-center text-white">
      <img src="https://www.google.com/favicon.ico" alt="Google" class="icon mb-2"></img>
      <span>Google</span>
    </div>
    <div class="bg-red-500 p-4 rounded-lg flex flex-col items-center text-white">
      <img src="https://www.facebook.com/favicon.ico" alt="Facebook" class="icon mb-2"></img>
      <span>Facebook</span>
    </div>
    <div class="bg-green-500 p-4 rounded-lg flex flex-col items-center text-white">
      <img src="https://www.twitter.com/favicon.ico" alt="Twitter" class="icon mb-2"></img>
      <span>Twitter</span>
    </div>
    <div class="bg-yellow-500 p-4 rounded-lg flex flex-col items-center text-white">
      <img src="https://www.linkedin.com/favicon.ico" alt="LinkedIn" class="icon mb-2"></img>
      <span>LinkedIn</span>
    </div>
    <div class="bg-purple-500 p-4 rounded-lg flex flex-col items-center text-white">
      <img src="https://www.github.com/favicon.ico" alt="GitHub" class="icon mb-2"></img>
      <span>GitHub</span>
    </div>
    <div class="bg-pink-500 p-4 rounded-lg flex flex-col items-center text-white">
      <img src="https://www.instagram.com/favicon.ico" alt="Instagram" class="icon mb-2"></img>
      <span>Instagram</span>
    </div>
    <div class="bg-indigo-500 p-4 rounded-lg flex flex-col items-center text-white">
      <img src="https://www.reddit.com/favicon.ico" alt="Reddit" class="icon mb-2"></img>
      <span>Reddit</span>
    </div>
    <div class="bg-teal-500 p-4 rounded-lg flex flex-col items-center text-white">
      <img src="https://www.stackoverflow.com/favicon.ico" alt="Stack Overflow" class="icon mb-2"></img>
      <span>Stack Overflow</span>
    </div>
    <div class="bg-gray-500 p-4 rounded-lg flex flex-col items-center text-white">
      <img src="https://www.pinterest.com/favicon.ico" alt="Pinterest" class="icon mb-2"></img>
      <span>Pinterest</span>
    </div> */}
          {/* <div class="bg-blue-500 h-24 flex items-center justify-center text-white">1</div>
          <div class="bg-red-500 h-24 flex items-center justify-center text-white">2</div>
          <div class="bg-green-500 h-24 flex items-center justify-center text-white">3</div>
          <div class="bg-yellow-500 h-24 flex items-center justify-center text-white">4</div>
          <div class="bg-purple-500 h-24 flex items-center justify-center text-white">5</div>
          <div class="bg-pink-500 h-24 flex items-center justify-center text-white">6</div>
          <div class="bg-indigo-500 h-24 flex items-center justify-center text-white">7</div>
          <div class="bg-teal-500 h-24 flex items-center justify-center text-white">8</div>
          <div class="bg-gray-500 h-24 flex items-center justify-center text-white">9</div> */}
        </div>
        {/* <div> */}
        {/* <Image src="https://localhost:5001/api/epdt/GetConfigFile?id=39a154da-a1a2-ef11-8a69-0017fa09967b"></Image> */}
        {/* </div> */}
      </div>
    );
  };
};

export default defineComponent(LandingPage);
