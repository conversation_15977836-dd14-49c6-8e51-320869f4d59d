MMicrosoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmabilitytruefSystem.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerializationfalse'System.Diagnostics.Debugger.IsSupportedfalse2System.Diagnostics.Tracing.EventSource.IsSupportedfalseSystem.Globalization.InvariantfalseSystem.Globalization.Hybridfalse)System.Net.Http.EnableActivityPropagationfalse6System.Reflection.Metadata.MetadataUpdater.IsSupportedfalse9System.Resources.ResourceManager.AllowCustomResourceTypesfalse&System.Resources.UseSystemResourceKeystrue<System.Runtime.InteropServices.BuiltInComInterop.IsSupportedfalseJSystem.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHostingfalse9System.Runtime.InteropServices.EnableCppCLIHostActivationfalseVSystem.Runtime.InteropServices.Marshalling.EnableGeneratedComInterfaceComImportInteropfalseESystem.Runtime.Serialization.EnableUnsafeBinaryFormatterSerializationfalse&System.StartupHookProvider.IsSupportedfalse-System.Text.Encoding.EnableUnsafeUTF7Encodingfalse<System.Text.Json.JsonSerializer.IsReflectionEnabledByDefaultfalse-System.Threading.Thread.EnableAutoreleasePoolfalse