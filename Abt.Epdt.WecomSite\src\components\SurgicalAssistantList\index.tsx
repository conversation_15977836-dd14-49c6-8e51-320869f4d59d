
import {  List, Loading } from "vant";
import {
  defineComponent,
  FunctionalComponent,
} from "vue";
import "#src/components/SurgicalAssistantList/index.css";

type ColumnProps = { name: string; width: string };
const SurgicalAssistantList: FunctionalComponent<{
  loading: boolean;
  finished: boolean;

  header: ColumnProps[];
  onLoad: (() => void) | undefined;
  onClick?: (item: any) => void;
  listData: { id:string; value: string; width: string,class:string }[][];
  //   listProps: PropType<typeof List>;
}> = (props) => {
  const {  loading, onLoad, header, listData, onClick,finished } = props;

  return () => {
    return (
      <List
        finished={finished}
        loading={loading}
        class=" "
        onLoad={onLoad}
      >
        <div class="">
          <div class="w-full  text-sm    bg-white  border-gray-300">
            <div class="flex z-20  w-fit   sticky top-[-1px]  bg-abbott text-white">
              <div
                class={`${header[0].width}  sticky top-0 left-0 bg-abbott z-20 flex-shrink-0 box-border  py-2 text-nowrap  border-b text-center`}
              >
                {header[0].name}
              </div>
              {header.slice(1).map((item) => (
                <div
                  class={`${item.width}  flex-shrink-0 box-border  py-2 text-nowrap px-4 border-b text-center`}
                >
                  {item.name}
                </div>
              ))}
            </div>
            <div class="w-full divide-y divide-gray-300  text-nowrap ">
              {listData.map((item, index) => (
                <>
                  <div
                    class={`flex  w-fit  ${
                      index % 2 === 0 ? "bg-gray-100" : "bg-white"
                    }`}
                    onClick={() => {
                      props.onClick && props.onClick(item);
                    }}
                  >
                    <div
                      class={`${
                        item[0].width
                      } z-10 flex-shrink-0 box-border py-2 sticky left-[-1px] text-overflow ${item[0].class} 
                  ${index % 2 === 0 ? "bg-gray-100" : "bg-white"}
                    `}
                    style="word-break: break-word; white-space: normal;"
                    >
                      {item[0].value}
                    </div>
                    {item.slice(1).map((detailItem) => (
                      <div
                        class={`${detailItem.width}  flex-shrink-0 py-2   ${detailItem.class??''} `}
                        // style="white-space:nowrap;overflow:hidden;text-overflow:ellipsis;"
                        style="word-break: break-word; white-space: normal;"
                      >
                        {detailItem.value}
                      </div>
                    ))}{" "}
                  </div>
                </>
              ))}
            </div>
          </div>
        </div>
      </List>
    );
  };
};

export default defineComponent(SurgicalAssistantList, {
  props: ["loading", "onLoad", "header", "listData", "onClick",'finished'],
});
