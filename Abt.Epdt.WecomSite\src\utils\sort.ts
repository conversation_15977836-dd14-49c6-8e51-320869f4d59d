import dayjs from "dayjs";

/**
 * 排序关联系统信息列表
 *
 * 此函数用于对关联系统信息进行排序，首先根据医院名称的首字母进行排序，
 * 如果医院名称首字母相同，则根据植入日期进行排序（较新的日期排在前面），
 * 如果植入日期也相同，则根据患者姓名的首字母进行排序，
 * 最后，如果患者姓名首字母也相同，则根据患者性别的首字母进行排序
 *
 * @param a 第一个需要比较的关联系统信息对象
 * @param b 第二个需要比较的关联系统信息对象
 * @returns 返回比较结果，用于数组排序方法
 */
export const sortAssociatedSystemInformationList = (
  a: AssociatedSystemInformationProps,
  b: AssociatedSystemInformationProps
) => {
  const hospitalComparison = a.hospital.text
    .charAt(0)
    .localeCompare(b.hospital.text.charAt(0));
  if (hospitalComparison !== 0) {
    return hospitalComparison;
  }
  let dateComparison =
    dayjs(b.implantDate).toDate().getTime() -
    dayjs(a.implantDate).toDate().getTime();
  if (dateComparison !== 0) {
    return dateComparison;
  }
  let nameComparison = a.patientName
    .charAt(0)
    .localeCompare(b.patientName.charAt(0));
  if (nameComparison !== 0) {
    return nameComparison;
  }
  return a.patientGender.charAt(0).localeCompare(b.patientGender.charAt(0));
};
