import { Button, Icon } from "vant";
import { defineComponent, FunctionalComponent } from "vue";
import { useRouter } from "vue-router";

const SubmitResult: FunctionalComponent = () => {
  const router = useRouter();
  return () => (
    <div class="h-full bg-white flex flex-col items-center">
      {/* <div> */}
      <Icon class="mt-[40%]" name="success" size={64} color="green"></Icon>
      {/* </div> */}
      <div class="text-lg  text-abbott font-semibold">提交成功!</div>
      <div class="mt-2 text-gray-400 text-sm">您的信息提交成功</div>
      {/* <div class="flex w-full justify-center">
        <Button
          onClick={() => {
            router.push({ name: "home" });
          }}
        >
          返回首页
        </Button>
      </div> */}
      <div
        class="text-abbott text-sm mt-auto mb-20"
        onClick={() => {
          router.push({ name: "home" });
        }}
      >
        点击返回首页
      </div>
    </div>
  );
};

export default defineComponent(SubmitResult);
