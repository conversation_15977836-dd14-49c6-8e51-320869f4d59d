{"name": "abt-epdt-wecomsite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:local": "vite --mode localfile", "dev:test": "vite --mode test", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "build:no-tsc": "vite build", "deploy:dev": "node ./src/build/upload.js --env=dev", "deploy:test": "node ./src/build/upload.js --env=staging", "preview": "vite preview"}, "imports": {"#*": "./*", "#components/*": "./src/components/*", "#pages/*": "./src/pages/*"}, "dependencies": {"@tanstack/vue-query": "^5.51.21", "@tato30/vue-pdf": "^1.11.2", "@techstark/opencv-js": "4.10.0-release.1", "@types/node": "^20.14.12", "axios": "^1.7.3", "compressorjs": "^1.2.1", "dayjs": "^1.11.12", "file-viewer": "^1.2.0", "jimp": "^1.6.0", "js-cookie": "^3.0.5", "jsqr": "^1.4.0", "mitt": "^3.0.1", "pinia": "^2.2.0", "pinia-plugin-persistedstate": "^4.1.3", "radash": "^12.1.0", "swiper": "^11.1.9", "unplugin-vue-components": "^0.27.4", "vant": "^4.9.3", "vue": "^3.5.13", "vue-router": "4", "workerpool": "^9.2.0"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@types/js-cookie": "^3.0.6", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "autoprefixer": "^10.4.19", "basic-ftp": "^5.0.5", "postcss": "^8.4.39", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-visualizer": "^5.12.0", "tailwindcss": "^3.4.6", "typescript": "^5.2.2", "unplugin-auto-import": "^0.18.1", "vite": "^5.4.11", "vite-plugin-mkcert": "^1.17.6", "vite-plugin-webpackchunkname": "^1.0.3", "vue-tsc": "^2.0.24"}}