// vite.config.ts
import { defineConfig } from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/vite@5.3.4_@types+node@20.14.12/node_modules/vite/dist/node/index.js";
import vue from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/@vitejs+plugin-vue@5.0.5_vite@5.3.4_@types+node@20.14.12__vue@3.4.33_typescript@5.5.4_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.0.0_vite@5.3.4_@types+node@20.14.12__vue@3.4.33_typescript@5.5.4_/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import AutoImport from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/unplugin-auto-import@0.18.1_rollup@4.19.0/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/unplugin-vue-components@0.27.3_@babel+parser@7.24.8_rollup@4.19.0_vue@3.4.33_typescript@5.5.4_/node_modules/unplugin-vue-components/dist/vite.js";
import { VantResolver } from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/@vant+auto-import-resolver@1.2.1/node_modules/@vant/auto-import-resolver/dist/index.esm.mjs";
import mkcert from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/vite-plugin-mkcert@1.17.6_vite@5.3.4_@types+node@20.14.12_/node_modules/vite-plugin-mkcert/dist/mkcert.mjs";

// src/plugins/copywasm.ts
import copy from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/rollup-plugin-copy@3.5.0/node_modules/rollup-plugin-copy/dist/index.commonjs.js";
function copyWASMPlugin() {
  return copy({
    verbose: true,
    hook: "closeBundle",
    targets: [
      {
        src: ["src/utils/barcodewasm/**/*", "!src/utils/barcodewasm/_framework"],
        dest: "dist"
      },
      {
        src: "src/utils/barcodewasm/_framework/**/*",
        dest: "dist"
      }
    ]
  });
}

// vite.config.ts
var vite_config_default = defineConfig({
  base: "./",
  envPrefix: "ONEPDT_",
  // resolve: { alias: { "#components": path.resolve(__dirname, "src/components") } },
  plugins: [
    mkcert(),
    vue(),
    vueJsx({
      // options are passed on to @vue/babel-plugin-jsx
    }),
    AutoImport({
      resolvers: [VantResolver()]
    }),
    Components({
      resolvers: [VantResolver()]
    }),
    copyWASMPlugin()
  ],
  server: {
    host: "localhost",
    port: 3e3,
    //本机端口
    proxy: {
      "/api": {
        target: "http://localhost:5000/"
        //代理目标服务器地址
        // changeOrigin: true,
        // secure: true,
        // rewrite: (path) => path.replace(/^\/api/, '')
        // rewrite:{  //替换路径中的/api
        //   '^/api':''
        // }
        /*pathRequiresRewrite: {
          '^/api': ''
        }*/
      }
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
