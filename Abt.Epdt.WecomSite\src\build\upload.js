import ftp from 'basic-ftp'; // 用于FTP操作
import fs from 'fs/promises'; // 用于文件系统操作
import path from 'path';

// console.log(JSON.stringify(import.meta.env));
const args = process.argv.slice(2);
const options = {};

args.forEach((arg) => {
  const [key, value] = arg.split('=');
  options[key.replace('--', '')] = value;
});

console.log('Options:', options);

const config = {
  dev: {
    accessConfig: {
      host: 'cnws-prod-sha20-007.ftp.chinacloudsites.chinacloudapi.cn',
      user: 'MD-CA183-EPDT-H5-D\\$MD-CA183-EPDT-H5-D',
      password: 'bnfNnvw4wf91mJklA8X20X1Pn7GfM9r3gmLxT81ssdzPabgqyeKo0Yr42N7E',
      secure: true, // 如果使用SFTP，设置为true}
    },
  },
  staging: {
    accessConfig: {
      host: 'cnws-prod-sha20-007.ftp.chinacloudsites.chinacloudapi.cn',
      user: 'md-ca183-epdt-h5-s\\$md-ca183-epdt-h5-s',
      password: 'z6xrCAm7pTidP3CWx0K1eK8gAwrY9dj2hoh84cLBui0tYBod0gMEioMoTHQN',
      secure: true, // 如果使用SFTP，设置为true}
    },
  },
};

const currentEnvConfig = config[options.env];
async function uploadToFTP() {
  const client = new ftp.Client();
  client.ftp.verbose = true; // 开启详细日志

  try {
    // 连接FTP服务器
    await client.access(currentEnvConfig.accessConfig);

    // 设置远程目录
    await client.ensureDir('/site/wwwroot/wwwroot/wecom');

    // 获取本地dist目录的路径
    const distPath = path.resolve('./dist');

    // 递归上传函数
    async function uploadDirectory(localDir, remoteDir) {
      const files = await fs.readdir(localDir, { withFileTypes: true });

      for (const file of files) {
        const localPath = path.join(localDir, file.name);
        const remotePath = path.join(remoteDir, file.name).replace(/\\/g, '/');

        if (file.isDirectory()) {
          // 如果是目录，创建远程目录并递归上传
          await client.ensureDir(remotePath);
          await uploadDirectory(localPath, remotePath);
        } else {
          // 如果是文件，直接上传
          await client.uploadFrom(localPath, remotePath);
          console.log(`Uploaded: ${localPath} -> ${remotePath}`);
        }
      }
    }

    // 开始上传
    await uploadDirectory(distPath, '/site/wwwroot/wwwroot/wecom');

    console.log('Upload completed successfully!');
  } catch (err) {
    console.error('Error during FTP upload:', err);
  } finally {
    client.close(); // 关闭FTP连接
  }
}

// 执行上传
uploadToFTP();
