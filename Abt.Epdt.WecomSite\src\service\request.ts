import { getLoginUrl } from '#src/utils/auth';
import axios, { AxiosResponse } from 'axios';
import Cookie from 'js-cookie';
import { showConfirmDialog, showToast } from 'vant';

const request = axios.create({
  baseURL: import.meta.env.ONEPDT_API_URL ?? '/',
  timeout: 120000, // 设置两分钟的超时时间
  withCredentials: false,
});

request.interceptors.request.use(
  (config) => {
    const token = Cookie.get('onepdtToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    // 在发送请求之前做些什么
    return config;
  },
  (error: any) => {
    // 处理请求错误
    return Promise.reject(error);
  }
);


request.interceptors.response.use(
  (response: AxiosResponse) => {
    //超过会话时间不操作就进行退出
    // clearSession();

    // 对响应数据做点什么
    return response;
  },
  (error: any) => {
    if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {
      // 处理超时错误，提示用户重新刷新页面
      showToast('请求超时，请刷新页面重试');
    }
    // 处理其他响应错误
    return Promise.reject(error);
  }
);

export default request;
