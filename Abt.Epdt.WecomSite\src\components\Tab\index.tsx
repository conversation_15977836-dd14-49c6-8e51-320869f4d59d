import { Button, Icon, Tabbar, TabbarItem } from 'vant';
import { defineComponent, FunctionalComponent, Ref, ref, UnwrapRef } from 'vue';
import { useUserStore } from '#src/stores/index.ts';
import { storeToRefs } from 'pinia';
const Tab: FunctionalComponent<{ activeIndex: Ref<UnwrapRef<string>> }> = (props) => {
  const { userInfo } = storeToRefs(useUserStore());
  console.log('Tab', userInfo.value);

  return () => (
    <>
      <Tabbar
        class="py-2 max-h-12 min-h-12   bottom-0 z-50"
        onChange={async (e) => {
          setTimeout(() => {
            props.activeIndex.value = e
          });
          // await new Promise((resolve) => setTimeout(resolve, 10));
        }}
        modelValue={props.activeIndex.value}
      >
        {userInfo.value?.istodo ? (
          <TabbarItem name="todo" dot={userInfo.value.isexitstodolist} icon="label-o">
            待办
          </TabbarItem>
        ) : (
          ''
        )}
        {userInfo.value?.isquery ? (
          <TabbarItem name="query" icon="search">
            查询
          </TabbarItem>
        ) : (
          ''
        )}
        <TabbarItem name="mime" icon="contact-o">
          我的
        </TabbarItem>
      </Tabbar>
    </>
  );
};

export default defineComponent(Tab, { props: ['activeIndex'] });
