import { defineComponent, FunctionalComponent, useSlots } from "vue";
import { Image, Watermark } from "vant";
import logo from "#/src/assets/logo_vertical.png";
import {FontModel} from "./Front"

const ElectronicwarrentyCardFrame: FunctionalComponent<{
  FrameData:FontModel
}> = (props) => {
  const slots = useSlots();
  return () => (
    <div class="border-2 bg-white w-full aspect-card box-border px-[4%] py-[2%] overflow-hidden border-[#CF579C] shadow-md rounded-md  relative">
      {/* <div class="h-[15%]"></div> */}
      <div class="z-10 h-[14%] relative w-full ">
        <div class="flex w-full items-center ">
          {/* <Image class="w-[10%]" src={logo}></Image> */}
          <div class="flex font-bold text-card-title flex-col items-center text-abbott flex-1">
            <h1 class="" style="margin-left:5%">雅培植入设备识别卡</h1>
            <p class="" style="margin-left:5%">Patient Identification Card</p>
          </div>
          <div class="absolute right-[16%] text-left">
            <p>No.</p>
          </div>
          <div class="absolute right-[2%] text-left">
            <p>{props.FrameData.cardNo}</p>
          </div>
        </div>
      </div>

      {slots.default()}
      <Watermark
        zIndex={9}
        width={180}
        height={60}
        content="仅供内部使用"
        fullPage={false}
      ></Watermark>
    </div>
  );
};
export default defineComponent(ElectronicwarrentyCardFrame, {
  props: ["FrameData"],
});
