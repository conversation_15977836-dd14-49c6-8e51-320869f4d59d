import Header from '#src/components/Header';
import { FunctionalComponent, onMounted, ref } from 'vue';

import { defineComponent } from 'vue';
import AccountNotExist from './AccountNotExist';
import { useRoute, useRouter } from 'vue-router';

const Error: FunctionalComponent = () => {
  const type = ref('');
  const message = ref('');

  onMounted(() => {
    const hash = window.location.hash;
    const queryString = hash.split('?')[1]; // 提取查询字符串部分
    const params = new URLSearchParams(queryString); // 使用 URLSearchParams 解析查询字符串
    console.log(window.location);
    type.value = params.get('type');
    message.value = decodeURIComponent(params.get('message'));
  });
  return () => {
    return (
      <div class="bg-white flex flex-col w-full h-full">
        <Header isShowBack={false}></Header>
        {type.value === 'accountnotexist' && <AccountNotExist message={message.value}></AccountNotExist>}
      </div>
    );
  };
};

export default defineComponent(Error);
