import { Button, Calendar, Field, Icon, List, Picker, Popup, showDialog, showToast, showFailToast, Empty } from 'vant';
import {
  computed,
  defineComponent,
  FunctionalComponent,
  onMounted,
  onUpdated,
  onBeforeMount,
  ref,
  Teleport,
  watch,
} from 'vue';
import Filter from './components/Filter';
import './index.css';
import { useGlobalSelect, useSelect } from '#src/hooks/index.ts';
import SurgicalAssistantList from '#src/components/SurgicalAssistantList';
import { useRouter } from 'vue-router';
import { useUserStore } from '#src/stores/index.ts';
import { storeToRefs } from 'pinia';
import Header from '#src/components/Header';
import request from '#src/service/request.js';
import { editMode } from '#src/global.js';
import { batchRender } from '#src/utils/batchProcess.ts';

export type SurgicalAssistantListProp = {
  hospital: string;
  model: string;
  type: string;
  createdby: Date;
  patientName: string;
  implantDate: string;
  surgeonNo: string;
  approveStatus: string;
};

export type FilterProps = {
  searchText: string;
  searchText2: string;
  productname: string;
  submittername: string;
  province: string;
  hospital: string;
  status: string;
  implantType: string;
  implantDateRange?: [string, string];
};
const headers = [
  { name: '医院', width: 'w-[20ch]' },
  { name: '植入时间', width: 'w-[11ch]' },
  { name: '型号', width: 'w-[15ch]' },
  { name: '术者', width: 'w-[12ch]' },
  { name: '患者', width: 'w-[8ch]' },
  { name: '填写人', width: 'w-[10ch]' },
  { name: '状态', width: 'w-[8ch]' },
  { name: '业务审批', width: 'w-[9ch]' },
  { name: '邮寄审批', width: 'w-[9ch]' },
  { name: '打印', width: 'w-[8ch]' },
  { name: '物流单号', width: 'w-[16ch]' },
  { name: '归档', width: 'w-[12ch]' },
  { name: '电子卡', width: 'w-[8ch]' },
  { name: '医院编码', width: 'w-[12ch]' },
  { name: '手术号', width: 'w-[16ch]' },
];

// var querydata = [];

const Search: FunctionalComponent = () => {
  const isShowFilter = ref(true);
  const isShowCalendar = ref(false);
  const implantDateRange = ref<[string, string]>(undefined);
  const searchFilterText = ref('');
  const hospital = ref<string>(undefined);
  const status = ref<string>(undefined);
  const implantType = ref<string>(undefined);
  const surgicalAssistantList = ref([]);
  const filterParams = ref<FilterProps>({
    searchText: '',
    searchText2: '',
    productname: '',
    submittername: '',
    province: '',
    hospital: '',
    status: '',
    implantType: '',
    implantDateRange: undefined,
  });

  const pageSize = ref(50);
  const pageIndex = ref(1);
  // 获取子组件的 ref
  const filterRef = ref(null);

  const router = useRouter();
  const { userInfo } = storeToRefs(useUserStore());
  const error = ref(false);
  const loading = ref(false);
  const finished = ref(false);
  const totalDataCount = ref(0);
  const operationCount = ref(0);
  const onListLoad = async () => {
    console.log('proxyemail', userInfo.value.proxyemail);
    fetchSurgicalAssistantList();
  };

  const select = useGlobalSelect();
  const fetchSurgicalAssistantList = async () => {
    if (!userInfo.value?.isLogin) return;
    if (loading.value || finished.value) return;
    loading.value = true;
    var starttime = '';
    var endtime = '';
    if (filterParams.value.implantDateRange != undefined) {
      starttime = filterParams.value.implantDateRange[0];
      endtime = filterParams.value.implantDateRange[1];
    }

    request({
      url:
        'api/epdt/GetOperationQuery?email=' +
        userInfo.value.email +
        '&proxyemail=' +
        userInfo.value.proxyemail +
        '&roleid=' +
        userInfo.value.roleid +
        '&pagesize=' +
        pageSize.value +
        '&pageindex=' +
        pageIndex.value +
        '&searchText=' +
        filterParams.value.searchText +
        // '&searchText2=' +
        // filterParams.value.searchText2 +
        '&productname=' +
        filterParams.value.productname +
        '&submittername=' +
        filterParams.value.submittername +
        '&province=' +
        filterParams.value.province +
        '&implantType=' +
        filterParams.value.implantType +
        '&starttime=' +
        starttime +
        '&endtime=' +
        endtime,
      method: 'get',
    })
      .then(async (res) => {
        console.log('res.data.datas', res.data.datas);
        loading.value = false;

        let xDatas = res.data.datas;

        if (xDatas === null) {
          finished.value = true;
          return;
        }
        if (xDatas.length < pageSize.value) {
          finished.value = true;
        }

        totalDataCount.value = res.data.total;
        operationCount.value = res.data.operationtotal;

        let querydata = [];

        if (res.data.datas && res.data.datas.length > 0) {
          //批量渲染DOM
          await batchRender(res.data.datas, 25, (batchDatas) => {
            querydata = batchDatas.map((item) => [
              { id: item.id, value: item.onepdt_hospitalname, width: 'w-[20ch]', class: 'pl-2' },
              { id: item.id, value: item.onepdt_date, width: 'w-[11ch]', class: 'text-center' },
              { id: item.id, value: item.onepdt_product_name, width: 'w-[15ch]', class: 'text-center' },
              { id: item.id, value: item.onepdt_hcp_text, width: 'w-[12ch]', class: 'text-center text-nowrap' },
              {
                id: item.id,
                value:
                  item.onepdt_patient != '' && item.onepdt_patient != null
                    ? item.onepdt_patient.substring(0, 1) + new Array(item.onepdt_patient.length).join('*')
                    : '',
                width: 'w-[8ch]',
                class: 'text-center',
              },
              { id: item.id, value: item.onepdt_submittername, width: 'w-[10ch]', class: 'text-center' },
              { id: item.id, value: item.onepdt_submit_status_label, width: 'w-[8ch]', class: 'text-center' },
              { id: item.id, value: item.onepdt_approval_status_label, width: 'w-[9ch]', class: 'text-center' },
              { id: item.id, value: item.onepdt_address_approval_status_label, width: 'w-[9ch]', class: 'text-center' },
              { id: item.id, value: item.onepdt_print_statuslabel, width: 'w-[8ch]', class: 'text-center' },
              { id: item.id, value: item.onepdt_logistics_no, width: 'w-[16ch]', class: 'text-center' },
              { id: item.id, value: item.onepdt_archival_statuslabel, width: 'w-[12ch]', class: 'text-center' },
              { id: item.id, value: item.onepdt_device_applicationname, width: 'w-[8ch]', class: 'text-center' },
              { id: item.id, value: item.onepdt_hospitalcode, width: 'w-[12ch]', class: 'text-center' },
              { id: item.id, value: item.onepdt_name, width: 'w-[16ch]', class: 'text-center' },
            ]);

            surgicalAssistantList.value.push(...querydata);
          });
        }

        pageIndex.value = pageIndex.value + 1;
      })

      // })
      .catch((error) => {
        showFailToast({
          message: error.response.data,
          duration: 3000,
        });
        finished.value = true;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  onMounted(() => {
    if (userInfo.value?.isLogin) {
      pageIndex.value = 1;
      finished.value = false;
      // onListLoad();
      fetchSurgicalAssistantList();
    }
  });
  onBeforeMount(() => {});
  watch(
    () => userInfo.value?.isLogin,
    (newValue) => {
      if (newValue === true) {
        pageIndex.value = 1;
        finished.value = false;
        onListLoad();
      }
    }
  );
  onUpdated(() => {
    console.log(loading);
  });

  watch(
    () => userInfo.value?.email,
    () => {
      pageIndex.value = 1;
      finished.value = false;
      onListLoad();
    }
  );

  return () => (
    <div class="flex-1 flex flex-col  overflow-hidden ">
      <div class="sticky top-0 ">
        <Header
          isShowBack={true}
          onClickBack={() => {
            router.replace({ name: 'landingpage' });
          }}
        ></Header>
        <div class=" text-sm">
          <div class="flex p-2 bg-white text-gray-400 ">
            <Button
              class="mx-2"
              size="small"
              type="primary"
              onClick={() => {
                surgicalAssistantList.value = [];
                pageIndex.value = 1;
                finished.value = false;
                fetchSurgicalAssistantList();
              }}
            >
              搜索
            </Button>
            <Button
              class="mx-2"
              size="small"
              type="primary"
              onClick={() => {
                if (filterRef.value) {
                  filterRef.value.resetFilterParams(); // 调用子组件暴露的方法
                }
              }}
            >
              清除
            </Button>
            <Button class="mx-2 invisible" size="small" type="primary"></Button>
            <div
              class="flex ml-auto mr-2 justify-center items-center sticky top-0"
              onClick={() => {
                isShowFilter.value = !isShowFilter.value;
              }}
            >
              <div>记录条数：{operationCount.value}</div>
              <Icon name="filter-o" />
              &nbsp;筛选 &nbsp;
              <Icon class={isShowFilter.value ? 'rotate-90' : '-rotate-90'} size="12" name="arrow-double-left"></Icon>
            </div>
          </div>

          <div class={`${!isShowFilter.value ? 'baseinfo-hidden' : 'baseinfo-show'}   min-h-0 baseinfo `}>
            <Filter
              ref={filterRef}
              status={status}
              filterParams={filterParams}
              // updateSelect={updateSelect}
              hospital={hospital}
              implantDateRange={implantDateRange}
              implantType={implantType}
              isShowCalendar={isShowCalendar}
              PageType={ref('查询')}
            ></Filter>
          </div>
        </div>
      </div>
      <div
        class="w-full flex flex-col flex-1  box-border px-2  pt-2  overflow-hideen overflow-y-scroll   "
        style="margin-bottom: 43px"
      >
        <div class="border flex-1  overflow-x-auto " style="margin-bottom: 20px;">
          {/* <div class="h-[200vh] bg-cyan-500"></div> */}
          {loading.value === false && surgicalAssistantList.value.length === 0 ? (
            <div class="bg-white h-full">
              {/* {' '} */}
              <Empty imageSize={128} description="暂无数据"></Empty>
            </div>
          ) : (
            <SurgicalAssistantList
              key={`${surgicalAssistantList.value.length} ${loading.value}`}
              listData={surgicalAssistantList.value}
              header={headers}
              finished={finished.value}
              onLoad={onListLoad}
              onClick={(item) => {
                console.log('onClickitem', item);
                router.push({
                  name: 'edit',
                  query: {
                    type: 'query',
                    editMode: editMode.editMail,
                    operationid: item[0].id,
                    timetemp: new Date().getUTCMilliseconds(),
                  },
                });
              }}
              loading={loading.value}
            ></SurgicalAssistantList>
          )}
        </div>
        {/* </div> */}
      </div>
      {/* loading  position. loading text will teleport to the loading div */}
      {/* <div class="h-16" id="loading"></div> */}
      {/* <div class="h-14"></div> */}
      {/* <div>
        <Calendar
          v-model:show={isShowCalendar.value}
          type="range"
          onConfirm={(data) => {
            filterParams.value.implantDateRange = data;
            isShowCalendar.value = false;
            console.log(data);
          }}
        />
      </div> */}
    </div>
  );
};

export default defineComponent(Search);
