import { customRef, ref } from 'vue';

export const extend = Object.assign;

export const convertParamsToSelectColumns = (params: CustomParam[]) => {
  return params.map((param) => ({
    text: param.epdt_name,
    value: param.epdt_value,
  }));
};

export const debounce = (fn, delay = 1000, immediate = true) => {
  let timeout;
  return function (...args) {
    const later = () => {
      timeout = null;
      if (!immediate) fn.apply(this, args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, delay);

    if (callNow) fn.apply(this, args);
  };
};

export const throttle = (func, delay) => {
  let timerId; // 用于存储定时器ID
  let lastExecTime = 0; // 记录上次执行时间

  return function (...args) {
    const context = this; // 保存 this 上下文
    const currentTime = Date.now(); // 获取当前时间
    const elapsed = currentTime - lastExecTime; // 计算距离上次执行的时间间隔

    // 如果距离上次执行时间已经超过设定的 delay，或者第一次执行
    if (elapsed >= delay || !timerId) {
      // 如果有定时器，先清除之前的定时器，确保只执行一次
      if (timerId) {
        clearTimeout(timerId);
        timerId = null;
      }

      func.apply(context, args); // 执行原函数，并应用正确的 this 上下文和参数
      lastExecTime = currentTime; // 更新上次执行时间
    } else if (!timerId) {
      // 如果距离上次执行时间不足 delay，并且没有定时器在等待，则设置一个新的定时器
      timerId = setTimeout(() => {
        func.apply(context, args); // 定时器到期后执行原函数
        lastExecTime = Date.now(); // 更新上次执行时间
        timerId = null; // 清除定时器ID，允许下次节流
      }, delay - elapsed); // 设置剩余的等待时间
    }
  };
};

export const isVantDialogCancel = (error) => error === 'cancel';

export class TimeoutError extends Error {
  constructor(message) {
    super(message);
  }
}

export const loadVConsole = () => {
  // 动态创建 script 标签
  const script = document.createElement('script');
  script.src = 'https://unpkg.com/vconsole@latest/dist/vconsole.min.js';
  script.onload = () => {
    // vConsole 加载完成后的回调
    // 初始化 vConsole
    window.vConsole = new window.VConsole();
    console.log('vConsole 已加载并初始化'); // 可选的日志
  };
  script.onerror = () => {
    console.error('vConsole 加载失败'); // 可选的错误处理
  };

  // 将 script 标签添加到 head 或 body 中
  document.head.appendChild(script); // 建议添加到 head 中
};
