.mask {
  /* @apply absolute inset-0 bg-black bg-opacity-70 clip-rect-mask; */
  width: 80%;
  height: 80%;
  outline: 50px solid rgba(0, 0, 0, 0.5);
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  cursor: move;
}

.clip-rect-mask {
  clip-path: polygon(
    0% 0%,
    100% 0%,
    100% 100%,
    0% 100%,
    /* 整个区域 */ 25% 25%,
    25% 75%,
    75% 75%,
    75% 25% /* 镂空区域 */ /* 0% 0%, 100% 0%, 100% 100%, 0% 100%,  */ /* 25% 25%, 25% 75%, 75% 75%, 75% 25%   */
  );
}

.barcode-mask {
    width: 70%;
    height: 70%;
    position: absolute;
    border: 1px dashed white;
    top: 15%;
    left: 15%;
}

.barcode-scan .van-uploader__input{
  /* pointer-events: none; */
}
.opencv-preview{
  /* transform: scale(0.75); */
}