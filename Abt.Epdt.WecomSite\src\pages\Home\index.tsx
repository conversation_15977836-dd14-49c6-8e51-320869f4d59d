import { defineComponent, FunctionalComponent, onMounted, Teleport, Transition,watch } from 'vue';
import { Button, Icon, Tabs } from 'vant';
import { Ref, ref } from 'vue';
import HospitalList from './components/HospitalList';
import Tab from '#components/Tab';
import Edit from '../Edit';
import { useRoute, useRouter } from 'vue-router';
import Search from '#pages/Search';
import Todo from '../Todo';
import { editMode } from '#src/global';
import Mine from '../Mine';
import Header from '#src/components/Header';
import { useUserStore } from '#src/stores/index.ts';
import { storeToRefs } from 'pinia';

const Home: FunctionalComponent = () => {
  const router = useRouter();
  const { userInfo } = storeToRefs(useUserStore());

  let tabActiveIndex = ref('');
  const docState = ref('saved');
  const show = ref(false);
  const route = useRoute();
  onMounted(() => {
   // console.log("Home",userInfo);
    // if(userInfo.value.isLogin){
    //   tabActiveIndex.value = (userInfo.value.istodo?'todo':'query')
    // }else{
    //   tabActiveIndex.value = 'todo'
    // }
    //tabActiveIndex.value = (userInfo.value.istodo?'todo':'query')
  });

  watch(
    () => userInfo.value?.isLogin,
    (newValue) => {
      if (newValue === true) {
        tabActiveIndex.value = (userInfo.value.istodo?'todo':'query')
      }
    }
  );

  return () => (
    <div class="flex flex-col h-screen">
      <div class="top-0 flex-1 flex flex-col max-h-screen" key="home">
        {tabActiveIndex.value === 'todo' && <Todo></Todo>}
        {tabActiveIndex.value === 'query' && <Search></Search>}
        {tabActiveIndex.value === 'mime' && <Mine></Mine>}
        <div class="fixed bottom-16 flex w-full justify-center"></div>

        <Tab activeIndex={tabActiveIndex}></Tab>
      </div>
    </div>
  );
};

export default defineComponent(Home, {});
