export const batchRender = async (data, batchSize = 50, renderCallback) => {
  let index = 0;

  const renderBatch = () => {
    return new Promise((resolve) => {
      requestAnimationFrame(() => {
        const batch = data.slice(index, index + batchSize);
        renderCallback(batch);
        index += batchSize;

        if (index < data.length) {
          resolve(renderBatch()); // 递归调用，继续渲染下一批
        } else {
          resolve(''); // 全部渲染完成
        }
      });
    });
  };

  await renderBatch(); // 等待所有批次渲染完成
};
