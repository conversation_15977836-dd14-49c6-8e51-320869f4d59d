import {
  computed,
  defineComponent,
  FunctionalComponent,
  getCurrentInstance,
  onActivated,
  onMounted,
  onUpdated,
  onBeforeMount,
  reactive,
  ref,
  Text,
  onUnmounted,
  Ref,
  watch,
  nextTick,
} from 'vue';
import {
  Button,
  Calendar,
  CellGroup,
  Field,
  FieldType,
  Form,
  Icon,
  Picker,
  Popup,
  Radio,
  RadioGroup,
  showToast,
  closeToast,
  Uploader,
  showDialog,
  showFailToast,
  Divider,
  Checkbox,
  Cell,
  FormInstance,
  NoticeBar,
  Dialog,
  Loading,
} from 'vant';
import dayjs from 'dayjs';
import request from '#src/service/request.js';
import { onBeforeRouteLeave, onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router';
import './index.css';
import Section from './components/Section';
import ProductCard from './components/ProductCard';
import { useGlobalSelect, useSelect } from '#src/hooks/useSelect.ts';
import { useParamsStore, useUserStore } from '#src/stores/index.ts';
import { storeToRefs } from 'pinia';
import Cookie from 'js-cookie';
import { convertParamsToSelectColumns, debounce, isVantDialogCancel } from '#src/utils/basic';
import Header from '#src/components/Header';
import $bus from '../../mitt.js';
import axios, { AxiosResponse } from 'axios';
import { ACTION, useReceiptFormStore } from '#src/stores/receiptForm';
import useLoading from '#src/hooks/useLoading.js';
import MoreSelect from '#src/components/MoreSelect/index.jsx';

type SelectColumnProp = { text: string; value: string };
//路由跳转类型
var routetype: any = '';
//跟台id
var operationid: any = '';
//电子识别卡
var Dapplications = [];
//历史跟台
var HistoryOperation = [];
//医院
var HospitalInfo = [];
//第一术者
var hcp1 = [];
//第二术者
var hcp2 = [];
//第三术者
var hcp3 = [];
//跟台耗时
var Gentai = [];
//希浦系统起搏
var Xipu = [];
//植入工具
var Tools = ref([]);
//心房生理性起搏
var Xinfang = [];
//ICD手术预防等级
var Icd = [];
//植入状态
var zhirutype = [];
//跟台类型
var GentaiType = [];
//现主机类型
var UnitType = [];
//原主机类型
var originalUnitType = [];
//是否禁用
var isdisabeld = false;
//是否可编辑
var isedit = true;
//校验提示
var tip: any = '';
var firstSurgeonisloading = ref(false);
var secondSurgeonisloading = ref(false);
var thirdSurgeonisloading = ref(false);
var Dapplicationisloading = ref(false);
var moreselectref = ref(null);

var authorizationLetterloading = ref(false);
var receiptFormloading = ref(false);

export type ImplantProduct = {
  id?: string;
  productid?: string;
  serialNumber?: string;
  type?: string;
  isWearAndTear?: boolean;
  isNonClinicalTrial?: boolean;
  isNonMainlandProduct?: boolean;
  model?: string;
  bigType?: string;
  category?: string;
  SAPProduct?: Ref<SAPProduct>[];
  randomId?: string;
  triggerUpdateInfo?: boolean;
};

export type SAPProduct = {
  id?: string;
  zmod?: string;
};

export type ValidateError = {
  name: string;
  message: string;
}[];

const Edit: FunctionalComponent = () => {
  isdisabeld = false;
  const paramsStore = useParamsStore();
  const onConfirmImplantDate = (date: Date) => {
    formInfo.value.implantDate = dayjs(date).format('YYYY-MM-DD');
    isShowCalendar.value = false;
  };
  const route = useRoute();

  //中间变量，用来在edit页面和识别页面传递图片数据
  const receiptFormStore = useReceiptFormStore();
  const receiptFormForPageTransfer = storeToRefs(receiptFormStore);


  


  const associatedSystemInformationList = async () => {
    console.log("associatedSystemInformationList")
    Dapplicationisloading.value = true;
    return request({
      url: 'api/epdt/GetDapplications?email='+'&operationid='+operationid,
      method: 'get',
    }).then((res) => {
      Dapplicationisloading.value = false;
      Dapplications = [];
      Dapplications = res.data;
    });
  };

  const GetHistoryOperation = async () => {
    return request({
      url: 'api/epdt/GetHistoryOperation',
      method: 'get',
    }).then((res) => {
      HistoryOperation = [];
      HistoryOperation = res.data;
    });
  };

  const LoadHospitalList = async () => {
    return request({
      url: 'api/epdt/GetHospitals?email=',
      method: 'get',
    }).then((res) => {
      HospitalInfo = [];
      HospitalInfo = res.data;
    });
  };

  const LoadHcp1List = async () => {
    firstSurgeonisloading.value = true;
    return request({
      url:
        'api/epdt/GetHCPs?email=' +
        userInfo.value.email +
        '&isout=' +
        isExternalDoctor1.value +
        '&hospitalcode=' +
        formInfo.value.implantHospitalCode,
      method: 'get',
    }).then((res) => {
      firstSurgeonisloading.value = false;
      hcp1 = [];
      hcp1 = res.data;
    })
  };
  const LoadHcp2List = async () => {
    secondSurgeonisloading.value = true;
    return request({
      url:
        'api/epdt/GetHCPs?email=' +
        userInfo.value.email +
        '&isout=' +
        isExternalDoctor2.value +
        '&hospitalcode=' +
        formInfo.value.implantHospitalCode,
      method: 'get',
    }).then((res) => {
      secondSurgeonisloading.value = false;
      hcp2 = [];
      hcp2 = res.data;
    });
  };
  const LoadHcp3List = async () => {
    thirdSurgeonisloading.value = true;
    return request({
      url:
        'api/epdt/GetHCPs?email=' +
        userInfo.value.email +
        '&isout=' +
        isExternalDoctor3.value +
        '&hospitalcode=' +
        formInfo.value.implantHospitalCode,
      method: 'get',
    }).then((res) => {
      thirdSurgeonisloading.value = false;
      hcp3 = [];
      hcp3 = res.data;
    });
  };

  const LoadGentai = async () => {
    return request({
      url: 'api/epdt/GetOnepdtConfig?tablename=跟台信息&fieldname=跟台耗时',
      method: 'get',
    }).then((res) => {
      Gentai = [];
      Gentai.push(res.data);
    });
  };

  const LoadXipu = async () => {
    return request({
      url: 'api/epdt/GetOnepdtConfig?tablename=跟台信息&fieldname=希浦系统起搏',
      method: 'get',
    }).then((res) => {
      Xipu = [];
      Xipu.push(res.data);
    });
  };

  const LoadTools = async () => {
    return request({
      url: 'api/epdt/GetOnepdtConfig?tablename=跟台信息&fieldname=植入工具',
      method: 'get',
    }).then((res) => {
      Tools.value = [];
      Tools.value=res.data;
    });
  };

  const LoadXinfang = async () => {
    return request({
      url: 'api/epdt/GetOnepdtConfig?tablename=跟台信息&fieldname=尝试心房生理性起搏',
      method: 'get',
    }).then((res) => {
      Xinfang = [];
      Xinfang.push(res.data);
    });
  };

  const LoadIcd = async () => {
    return request({
      url: 'api/epdt/GetOnepdtConfig?tablename=跟台信息&fieldname=ICD手术预防等级',
      method: 'get',
    }).then((res) => {
      Icd = [];
      Icd.push(res.data);
    });
  };

  const LoadZhiruType = async () => {
    return request({
      url: 'api/epdt/GetOnepdtConfig?tablename=跟台信息&fieldname=植入类型',
      method: 'get',
    }).then((res) => {
      zhirutype = [];
      zhirutype.push(res.data);
    });
  };

  const LoadGentaiType = async () => {
    return request({
      url: 'api/epdt/GetOnepdtConfig?tablename=跟台信息&fieldname=跟台类型',
      method: 'get',
    }).then((res) => {
      GentaiType = [];
      GentaiType.push(res.data);
    });
  };

  //现主机类型
  const LoadmainUnitType = async () => {
    return request({
      url: 'api/epdt/GetOnepdtConfig?tablename=产品信息&fieldname=产品大类',
      method: 'get',
    }).then((res) => {
      UnitType = [];
      originalUnitType = [];
      UnitType.push(res.data);
      originalUnitType.push(res.data);
    });
  };

  const LoadOperation = async () => {
    return request({
      url: 'api/epdt/GetOperation?operationid=' + operationid + '&email=' + userInfo.value.email,
      method: 'get',
    })
      .then(async (res) => {
        console.log('operation', res.data);
        formInfo.value = res.data;
        console.log("formInfo.value",formInfo.value)
        Dapplication.value.patientName = res.data.devpatientName;
        Dapplication.value.patientGender = res.data.devpatientGender === '男' ? '0' : '1';
        Dapplication.value.implantDate = res.data.devimplantDate;
        Dapplication.value.implantHospital = res.data.devimplantHospital;
        Dapplication.value.implantHospitalCode = res.data.devimplantHospitalCode;
        Dapplication.value.implantHospitalId = res.data.devimplantHospitalId;

        isedit = res.data.isedit;
        isdisabeld =
          formInfo.value.onepdt_address_approval_status == 2 || formInfo.value.onepdt_submit_status_label == '跟台';
        if (
          formInfo.value.deviceapplicationid &&
          formInfo.value.deviceapplicationid != '' &&
          formInfo.value.deviceapplicationid != undefined
        ) {
          relatedSystemInfo.value = `${res.data.devpatientName}-${res.data.devpatientGender == '0' ? '男' : '女'}-${
            res.data.devimplantDate
          }-${res.data.devimplantHospital}`;
        }
        if (
          formInfo.value.relatedoperationid &&
          formInfo.value.relatedoperationid != '' &&
          formInfo.value.relatedoperationid != undefined
        ) {
          relatedOperation.value = `${res.data.relatedoperationpatient}-${res.data.relatedoperationdate}-${res.data.relatedoperationhospitalname}-${res.data.relatedoperationproduct_name}-${res.data.relatedoperationproduct_sn}`;
        }
        if (res.data.details) {
          products.value = res.data.details;
        }
        surgeonDuration.value = res.data.surgeonDuration;
        hisPurkinjeSystemPacing.value = res.data.hisPurkinjeSystemPacing;
        success_tool.value = res.data.success_tool_list;
        failed_tool.value = res.data.failed_tool_list;
        console.log("看看",formInfo.value.failtoolslist);
        ftoolslist.value = formInfo.value.failtoolslist;
        // if(moreselectref.value && formInfo.value.failtoolslist){
        //   moreselectref.value.setcheckBox.setboxValue(formInfo.value.failtoolslist.map(o=>o.text));
        // }
        xfsystem.value = res.data.xfsystem;
        icdSurgeryProphylacticLevel.value = res.data.icdSurgeryProphylacticLevel;
        implantType.value = res.data.implantType;
        originalMainUnitType.value = res.data.originaldevice;
        mainUnitType.value = res.data.replaceddevice;
        isIcdPatientWithCadHistory.value = res.data.isIcdPatientWithCadHistory;
        ksheath.value = res.data.ksheathvalue == '1'?true:false;
        isWarrantyCardMailed.value = res.data.isWarrantyCardMailed;
        if (res.data.receType == '1') {
          receType.value = '自定义';
        } else if (res.data.receType == '0') {
          receType.value = '植入医院';
        }
        //receType.value = res.data.receType == '1' ? '自定义' : '植入医院';
        ksheath.value = res.data.ksheathlabel;
        if (formInfo.value.implantHospitalCode != undefined) {
          await LoadHcp1List();
          await LoadHcp2List();
          await LoadHcp3List();
        }
        if (res.data.authorizationLetter && res.data.authorizationLetter.length > 0) {
          //var Letterarr = [];
          authorizationLetter.value = [];
          res.data.authorizationLetter.forEach(async (v) => {
            // let img = v.content;
            // let imgurl = base64ImgtoFile(img);
            // console.log('imgurl===', imgurl);
            // let url = window.webkitURL.createObjectURL(imgurl) || window.URL.createObjectURL(imgurl);
            // // 创建图片地址
            // var letter = {
            //   id: v.id,
            //   content: v.content,
            //   file: imgurl,
            //   name: v.file.name,
            //   isImage: true,
            //   url,
            // };
            // Letterarr.push(letter);
            await processImages(v.id,"授权书");
          });
          //authorizationLetter.value = Letterarr;
        }
        if (res.data.receiptForm && res.data.receiptForm.length > 0) {
          receiptForm.value = [];
          //var receiptarr = [];
          res.data.receiptForm.forEach(async (v) => {
            // let img = v.content;
            // let imgurl = base64ImgtoFile(img);
            // console.log('imgurl===', imgurl);
            // let url = window.webkitURL.createObjectURL(imgurl) || window.URL.createObjectURL(imgurl);
            // // 创建图片地址
            // var receipt = {
            //   id: v.id,
            //   content: v.content,
            //   file: imgurl,
            //   name: v.file.name,
            //   isImage: true,
            //   url,
            // };
            // receiptarr.push(receipt);
            await processImages(v.id,"回执单");
          });
          // console.log('v', receiptarr);
          // receiptForm.value = receiptarr;
        }
      })
      .finally(() => {
        isShowLoading.value = false;
      });
  };

  const processImages = async (photoid,phototype) =>{
    if(phototype == "授权书"){        
      authorizationLetterloading.value = true;
    }else if(phototype == "回执单"){
      receiptFormloading.value = true;
    }
    request({
      url: 'api/epdt/GetPhoto?id=' + photoid,
      method: 'get',
    }).then((res)=>{
      if(res.data&&res.data.id){
        var v = res.data;
        let img = v.content;
        let imgurl = base64ImgtoFile(img);
        let url = window.webkitURL.createObjectURL(imgurl) || window.URL.createObjectURL(imgurl);
        // 创建图片地址
        var imageinfo = {
          id: v.id,
          content: v.content,
          file: imgurl,
          name: v.file.name,
          isImage: true,
          url,
        };
        if(phototype == "授权书"){        
          authorizationLetter.value.push(imageinfo);
          authorizationLetterloading.value = false;
        }else if(phototype == "回执单"){
          receiptForm.value.push(imageinfo);
          receiptFormloading.value = false;
        }
      }else{
        if(phototype == "授权书"){        
          authorizationLetterloading.value = false;
        }else if(phototype == "回执单"){
          receiptFormloading.value = false;
        }
      }
    }).catch(err=>{
      if(phototype == "授权书"){        
        authorizationLetterloading.value = false;
      }else if(phototype == "回执单"){
        receiptFormloading.value = false;
      }
    })
  }

  function generateRandom16Digits() {
    let randomNumber = '';
    for (let i = 0; i < 16; i++) {
      randomNumber += Math.floor(Math.random() * 10); // 生成0-9之间的随机数
    }
    return randomNumber;
  }

  onActivated(() => {
    // console.log(receiptFormForPageTransfer.receiptForm.value.fileData);
    if (receiptFormForPageTransfer.receiptForm.value.action === ACTION.BACK_TO_EDIT) {
      //处理从识别二级页面返回回来的确认事件
      // deleteProducts.value = [
      //   ...deleteProducts.value,
      //   ...Array.from(products.value.map((item) => ({ ...item, deflag: true }))),
      // ]; //浅复制数组，因为都是基本数据
      products.value = []; //清空产品列表
      formInfo.value.deletePreviousProduct = true;
      receiptForm.value = receiptFormForPageTransfer.receiptForm.value.fileData;
      receiptFormStore.receiptForm.barcodeArr.forEach((barcode) => {
        products.value.push({ serialNumber: barcode, randomId: generateRandom16Digits() });
      });
      nextTick(() => {
        // debugger;
        setTimeout(() => {
          console.log('call event');
          products.value.forEach((product) => (product.triggerUpdateInfo = true));
          // $bus.emit('updateProductInfo');
        }, 2000);
      });
      receiptFormStore.updateReceiptForm({ fileData: [], barcodeArr: [], action: ACTION.UNDEFINED });
    }
    // console.log(args);
  });

  const base64ImgtoFile = (dataurl, filename = 'file') => {
    const arr = dataurl.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const suffix = mime.split('/')[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], `${filename}.${suffix}`, {
      type: mime,
    });
  };

  const PhotoDelete = async (photo) => {
    console.log(photo);
    if (photo.id) {
      request({
        url: 'api/epdt/Delete?entityname=onepdt_t_opreation_photo&entityid=' + photo.id,
        method: 'get',
      })
        .then(() => {
          $bus.emit('EditOperation', '删除图片成功');
          showToast({ message: '删除成功' });
        })
        .catch((error) => {
          showFailToast({
            message: error.response.data,
            duration: 3000,
          });
        });
    }
  };

  const onOversize = async(photo) =>{
    console.log(photo);
    showFailToast({
      message: "图片不能超过10M",
      duration: 3000,
      style: { width: '150px', textAlign: 'center' },
    });
  };

  const { editMode } = route.query;
  // console.log(editMode)

  const router = useRouter();

  const { userInfo } = storeToRefs(useUserStore());

  const isShowLoading = ref(false);
  
  useLoading(isShowLoading);


  const isShowNoteLoading = ref(false);
  const loadingText = ref('');

  const isShowCalendar = ref(false);
  const surgeonDuration = ref<string>(undefined);
  const patientGender = ref<number>(undefined);
  const implantType = ref<string>(undefined);
  const receiptForm = ref<any>([]);
  const authorizationLetter = ref<any>([]);
  const messageNote = ref<string>('');
  const surgicalAssistant = ref<string>(undefined);
  const isShowPopup = ref(false);
  const isExternalDoctor1 = ref(false);
  const isExternalDoctor2 = ref(false);
  const isExternalDoctor3 = ref(false);
  const hisPurkinjeSystemPacing = ref<string>(undefined);
  const success_tool = ref<string>(undefined);
  const failed_tool = ref<string>(undefined);
  const xfsystem = ref<string>(undefined);
  const ksheath = ref(false);
  const isIcdPatientWithCadHistory = ref(false);
  const icdSurgeryProphylacticLevel = ref<string>(undefined);
  const isWarrantyCardMailed = ref(false);
  const mainUnitType = ref<string>(undefined);
  const originalMainUnitType = ref<string>(undefined);
  const relatedSystemInfo = ref<string>(undefined);
  const relatedOperation = ref<string>(undefined);
  const deviceapplicationid = ref<string>(undefined);
  const address = ref<string>(undefined);
  const tel = ref<string>(undefined);
  const receType = ref<string>(undefined);
  const rece = ref<string>(undefined);
  const isShowBarcodeScanPage = ref(false);
  const ftoolslist = ref<any>([]);

  const formRef = ref<FormInstance>();

  const Dapplication = ref<{
    patientName: string;
    patientGender: string;
    implantHospital: string;
    implantHospitalCode: string;
    implantHospitalId: string;
    implantDate: string;
  }>({
    implantHospital: undefined,
    implantHospitalCode: undefined,
    implantHospitalId: undefined,
    implantDate: undefined,
    patientName: undefined,
    patientGender: '0',
  });

  const formInfo = ref<{
    email: string;
    deviceapplicationid: string;
    relatedoperationid:string;
    implantHospital: string;
    implantHospitalCode: string;
    implantHospitalId: string;
    implantDate: string;
    firstSurgeon: string;
    firstSurgeonid: string;
    secondSurgeon: string;
    secondSurgeonid: string;
    thirdSurgeon: string;
    thirdSurgeonid: String;
    surgeonDurationid: string;
    hisPurkinjeSystemPacingid: string;
    success_tool_list: string;
    failed_tool_list: string;
    failtoolslist:any;
    xfsystemId:string;
    ksheathvalue: string;
    ksheathlabel: string;
    icdSurgeryProphylacticLevelid: string;
    implantTypeid: string;
    implantType: string;
    originaldeviceid: string;
    replaceddeviceid: string;
    isWarrantyCardMailed: boolean;
    receType: string;
    address: string;
    rece: string;
    tel: string;
    messageNote: string;
    rejectreason: string;
    onepdt_address_approval_comment: string;
    onepdt_address_approval_tag: string;
    onepdt_approval_comment: string;
    onepdt_approval_tag: string;
    onepdt_approval_status: number;
    onepdt_approval_statuslabel: string;
    onepdt_address_approval_status: number;
    onepdt_address_approval_statuslabel: string;
    onepdt_submit_status_label: string;
    surgicalAssistantType: string;
    surgicalAssistantTypeid: string;
    isSelfSurgicalAssistant: boolean;
    isIcdPatientWithCadHistory: boolean;
    onepdt_is_returned: boolean;
    surgicalAssistant: string;
    patientName: string;
    patientGender: string;
    authorizationLetter: any;
    receiptForm: any;
    details: any;
    deletePreviousProduct: boolean;
  }>({
    email: undefined,
    deviceapplicationid: undefined,
    relatedoperationid:undefined,
    implantHospital: undefined,
    implantHospitalCode: undefined,
    implantHospitalId: undefined,
    implantDate: undefined,
    firstSurgeon: undefined,
    firstSurgeonid: undefined,
    secondSurgeon: undefined,
    secondSurgeonid: undefined,
    thirdSurgeon: undefined,
    thirdSurgeonid: undefined,
    surgeonDurationid: undefined,
    hisPurkinjeSystemPacingid: undefined,
    success_tool_list: undefined,
    failed_tool_list: undefined,
    xfsystemId:undefined,
    ksheathvalue: undefined,
    ksheathlabel: undefined,
    icdSurgeryProphylacticLevelid: undefined,
    implantTypeid: undefined,
    implantType: undefined,
    originaldeviceid: undefined,
    replaceddeviceid: undefined,
    isWarrantyCardMailed: false,
    receType: undefined,
    address: undefined,
    rece: undefined,
    tel: undefined,
    messageNote: undefined,
    onepdt_address_approval_comment: undefined,
    onepdt_address_approval_tag: undefined,
    onepdt_approval_comment: undefined,
    onepdt_approval_tag: undefined,
    rejectreason: undefined,
    onepdt_approval_status: undefined,
    onepdt_approval_statuslabel: undefined,
    onepdt_address_approval_status: undefined,
    onepdt_address_approval_statuslabel: undefined,
    surgicalAssistantType: undefined,
    surgicalAssistantTypeid: undefined,
    surgicalAssistant: undefined,
    isIcdPatientWithCadHistory: false,
    onepdt_is_returned: false,
    patientName: undefined,
    patientGender: '0',
    onepdt_submit_status_label: '周报',
    authorizationLetter: undefined,
    receiptForm: undefined,
    details: undefined,
    failtoolslist:undefined,
    isSelfSurgicalAssistant: true,
    deletePreviousProduct: false,
  });

  watch(
    () => userInfo.value?.email,
    () => {
      formInfo.value.email = userInfo.value.email;
      formInfo.value.surgicalAssistant = userInfo.value.name;
      // userInfo.value.email;
    }
  );
  const moreselecttools = computed(() =>{
    var resarr = [];
    if(Tools.value&&Tools.value.length>0){
      if(success_tool.value){
        resarr = Tools.value.filter(a=>a.text!==success_tool.value).map(o=>o.text);
      }else{
        resarr = Tools.value.map(o=>o.text)
      }
    }
     return resarr;
  });
  const succTools = computed(() =>{
    var resarr = [];
    if(Tools.value&&Tools.value.length>0){
      if(failed_tool.value){
        var farr = failed_tool.value.split("、");
        resarr = Tools.value.filter(item => !farr.includes(item.text));
      }else{
        resarr = Tools.value
      }
    }
     return resarr;
  });

  const select = useGlobalSelect();
  const products = ref<ImplantProduct[]>([]);
  const deleteProducts = ref<ImplantProduct[]>([]);
  const userSectionRef = ref();
  const implantSectionRef = ref(null);
  const ProductSectionRef = ref(null);
  // let onCurrentSelectConfirm = (option) => {};

  const updateReceiptForm = (newValue) => {
    receiptForm.value = newValue;
  };

  const onConfirmSaveAsDraft = async () => {
    isShowLoading.value = true;
    try {
      const res = await formRef.value.validate();
      
      if (
        (Dapplication.value.patientName != formInfo.value.patientName ||
          Dapplication.value.patientGender != formInfo.value.patientGender ||
          Dapplication.value.implantDate != formInfo.value.implantDate ||
          Dapplication.value.implantHospitalCode != formInfo.value.implantHospitalCode ||
          Dapplication.value.implantHospital != formInfo.value.implantHospital) &&
        (formInfo.value.messageNote == '' ||
          formInfo.value.messageNote == undefined ||
          formInfo.value.messageNote.trim().length == 0) &&
        relatedSystemInfo.value
      ) {
        isShowLoading.value = false;
        isShowNoteLoading.value = true;
        return;
      }
      formInfo.value.details = products.value;
      formInfo.value.receiptForm = [];
      formInfo.value.authorizationLetter = [];
      formInfo.value.email = userInfo.value.email;

      // request({
      //   method: 'post',
      //   url: '/api/epdt/EditOperation',
      //   data: formInfo.value,
      // })
      //   .then((res) => {
      //     operationid = res.data;
      //     uploadphoto();
      //     $bus.emit('EditOperation', '保存成功');
      //     $bus.emit('Todolist', '保存成功');
      //     isShowLoading.value = false;
      //     showToast({
      //       message: '保存成功',
      //       duration: 1000,
      //     });
      //     router.back();
      //   })
      //   .catch((error) => {
      //     isShowLoading.value = false;
      //     showFailToast({
      //       message: error.response.data,
      //       duration: 3000,
      //     });
      //   });
      try{
        // **先执行 EditOperation 并获取 operationid**
        const response = await request({
          method: 'post',
          url: '/api/epdt/EditOperation',
          data: formInfo.value,
        });

        operationid = response.data; // 从 API 获取 operationid

        // **如果 operationid 存在，则执行 uploadphoto**
        if (operationid) {
          await uploadphoto(operationid);
        }

        $bus.emit('EditOperation', '保存成功');
        $bus.emit('Todolist', '保存成功');
        isShowLoading.value = false;
        showToast({ message: '保存成功', duration: 1000 });
        router.back();
      }catch(error){
        isShowLoading.value = false;
        const errorMessage = error.response?.data || error.message || '操作失败';
        showFailToast({ message: errorMessage, duration: 3000 });
      }       
    } catch (err) {
      isShowLoading.value = false;
      basicValidateFailed(err);
    }
  };

  //上传图片
  // const uploadphoto = async () => {
  //   const token = Cookie.get('onepdtToken');
  //   var reqheader = {
  //     'Content-Type': 'multipart/form-data',
  //   } as { 'Content-Type': string; Authorization?: string };
  //   if (token) {
  //     reqheader.Authorization = `Bearer ${token}`;
  //   }
  //   if (operationid && operationid != undefined && operationid != null && operationid != '') {
  //     if (authorizationLetter.value != undefined) {
  //       authorizationLetter.value.forEach((v) => {
  //         console.log('v.file', v.file);
  //         const formData = new FormData();
  //         formData.append('operationid', operationid);
  //         formData.append('photoid', v.id != undefined ? v.id : '');
  //         formData.append('classify', '授权书');
  //         if (v.id != undefined) {
  //           const file = new File([v.content], v.file.name, { type: v.file.type });
  //           formData.append('file', file);
  //         } else {
  //           formData.append('file', v.file);
  //         }

  //         const response = axios
  //           .post('/api/epdt/upload', formData, {
  //             headers: reqheader,
  //           })
  //           .then((res) => {
  //             v.id = res.data;
  //           })
  //           .catch((error) => {
  //             showFailToast({
  //               message: error.response.data,
  //               duration: 3000,
  //             });
  //           });
  //       });
  //     }
  //     if (receiptForm.value != undefined) {
  //       receiptForm.value.forEach((v) => {
  //         const formData = new FormData();
  //         formData.append('operationid', operationid);
  //         formData.append('photoid', v.id != undefined ? v.id : '');
  //         formData.append('classify', '回执单');
  //         if (v.id != undefined) {
  //           const file = new File([v.content], v.file.name, { type: v.file.type });
  //           formData.append('file', file);
  //         } else {
  //           formData.append('file', v.file);
  //         }
  //         const response = axios
  //           .post('/api/epdt/upload', formData, {
  //             headers: reqheader,
  //           })
  //           .then((res) => {
  //             v.id = res.data;
  //           })
  //           .catch((error) => {
  //             showFailToast({
  //               message: error.response.data,
  //               duration: 3000,
  //             });
  //           });
  //       });
  //     }
  //   }
  // };
  const uploadphoto = async (operationid) => {
    const token = Cookie.get('onepdtToken');
    var reqheader = {
      'Content-Type': 'multipart/form-data',
    } as { 'Content-Type': string; Authorization?: string };
    if (token) {
      reqheader.Authorization = `Bearer ${token}`;
    }
  
    if (!operationid) throw new Error('操作ID无效，无法上传图片'); // 确保 operationid 存在
  
    const uploadTasks = [];
  
    if (authorizationLetter.value) {
      for (const v of authorizationLetter.value) {
        const formData = new FormData();
        formData.append('operationid', operationid);
        formData.append('photoid', v.id ? v.id : '');
        formData.append('classify', '授权书');
        if (v.id) {
          const file = new File([v.content], v.file.name, { type: v.file.type });
          formData.append('file', file);
        } else {
          formData.append('file', v.file);
        }
  
        uploadTasks.push(
          axios.post('/api/epdt/upload', formData, { headers: reqheader })
            .then(res => {
              v.id = res.data;
            })
            .catch(error => {
              throw new Error(`授权书上传失败: ${error.response?.data || error.message}`);
            })
        );
      }
    }
  
    if (receiptForm.value) {
      for (const v of receiptForm.value) {
        const formData = new FormData();
        formData.append('operationid', operationid);
        formData.append('photoid', v.id ? v.id : '');
        formData.append('classify', '回执单');
        if (v.id) {
          const file = new File([v.content], v.file.name, { type: v.file.type });
          formData.append('file', file);
        } else {
          formData.append('file', v.file);
        }
  
        uploadTasks.push(
          axios.post('/api/epdt/upload', formData, { headers: reqheader })
            .then(res => {
              v.id = res.data;
            })
            .catch(error => {
              throw new Error(`回执单上传失败: ${error.response?.data || error.message}`);
            })
        );
      }
    }
  
    // **确保所有图片上传完成，否则抛出错误**
    await Promise.all(uploadTasks);
  };
  

  const onDeleteDraft = async () => {
    showDialog({
      message: '是否确认删除?',
      showCancelButton: true,
    }).then(() => {
      request({
        url: 'api/epdt/Delete?entityname=onepdt_t_operation&entityid=' + operationid,
        method: 'get',
      })
        .then(() => {
          $bus.emit('EditOperation', '删除周报成功');
          $bus.emit('Todolist', '删除周报成功');
          showToast({ message: '删除成功', duration: 1000 });
          router.back();
        })
        .catch((error) => {
          showFailToast({
            message: error.response.data,
            duration: 3000,
          });
        });
    });
  };

  const ClearOperationInfo = async () =>{
    router.replace({
      name: 'edit',
      query: {
        type: 'create',
        operationid: '',
        editMode: "new",
        timetemp: new Date().getUTCMilliseconds(),
      },
    });
  };

  const SubmitOperation = async () => {
    isShowLoading.value = true;
    loadingText.value = '加载中';
    try {
      const res = await formRef.value.validate();
      formInfo.value.details = products.value;
      formInfo.value.receiptForm = [];
      formInfo.value.authorizationLetter = [];
      formInfo.value.email = userInfo.value.email;
      try{
        const editRes = await request({
          method: 'post',
          url: '/api/epdt/EditOperation',
          data: formInfo.value,
        });
    
        operationid = editRes.data;
    
        // **🚀 步骤 3: 上传照片**
        await uploadphoto(operationid); // 确保照片上传完成再继续
    
        // **🚀 步骤 4: 执行 SubmitOperation**
        showToast({
          duration: 0,
          message: '提交中...',
          forbidClick: true,
        });
    
        await request({
          url: `api/epdt/SubmitOperation?operationid=${operationid}`,
          method: 'get',
        });
    
        // **🚀 提交成功**
        isShowLoading.value = false;
        $bus.emit('EditOperation', '提交成功');
        $bus.emit('Todolist', '提交成功');
        closeToast();
        showToast({ message: '提交成功', duration: 1000 });
        router.back();
      }catch(error){
        isShowLoading.value = false;
        const errorMessage = error.response?.data || error.message || '操作失败';
        showFailToast({ message: errorMessage, duration: 3000 });
      }
      // request({
      //   method: 'post',
      //   url: '/api/epdt/EditOperation',
      //   data: formInfo.value,
      // })
      //   .then((res) => {
      //     operationid = res.data;
      //     uploadphoto();
      //     //$bus.emit('EditOperation', '保存成功');
      //     //$bus.emit('Todolist', '保存成功');
      //     showToast({
      //       duration: 0,
      //       message: '提交中...',
      //       forbidClick: true,
      //     });
      //     request({
      //       url: 'api/epdt/SubmitOperation?operationid=' + operationid,
      //       method: 'get',
      //     })
      //       .then((res) => {
      //         isShowLoading.value = false;
      //         $bus.emit('EditOperation', '提交成功');
      //         $bus.emit('Todolist', '提交成功');
      //         closeToast();
      //         showToast({ message: '提交成功', duration: 1000 });
      //         router.back();
      //       })
      //       .catch((error) => {
      //         isShowLoading.value = false;
      //         showFailToast({
      //           message: error.response.data,
      //           duration: 3000,
      //         });
      //       });
      //   })
      //   .catch((error) => {
      //     isShowLoading.value = false;
      //     showFailToast({
      //       message: error.response.data,
      //       duration: 3000,
      //     });
      //   });
    } catch (err) {
      isShowLoading.value = false;
      basicValidateFailed(err);
    }
  };

  const basicValidateFailed = (error: ValidateError) => {
    try {
      if (error.length) {
        console.log(error);
        // showToast({
        //   message: '数据校验不通过，请填写内容',
        //   duration: 3000,
        // });
        userSectionRef.value.openSection();
        implantSectionRef.value.openSection();

        formRef.value.scrollToField(error[0].name, {
          behavior: 'smooth',
          block: 'center',
        });
      }
    } catch (error) {
      console.error(error);
    }
  };
  const initData = async () => {
    isShowLoading.value = true;
    loadingText.value = '加载中';
    isdisabeld = false;
    isedit = true;
    operationid = route.query.operationid;
    routetype = route.query.type;
    console.log('Edit', operationid);

    try {
      await Promise.all([
        associatedSystemInformationList(),
        GetHistoryOperation(),
        LoadHospitalList(),
        //LoadHcp1List(),
        //LoadHcp2List(),
        //LoadHcp3List(),
        LoadGentai(),
        LoadXipu(),
        LoadTools(),
        LoadXinfang(),
        LoadIcd(),
        LoadZhiruType(),
        LoadGentaiType(),
        LoadmainUnitType(),
      ]);
      if (operationid != null && operationid != undefined && operationid != '') {
        await LoadOperation();
        await LoadHcp1List();
        await LoadHcp2List();
        await LoadHcp3List();
        //await GetHistoryOperation();
      }
    } catch (error) {
      console.error(error);
    } finally {
      isShowLoading.value = false;
    }
  };

  onMounted(async () => {
    await initData();

    $bus.on('EditOperation', (value) => {
      LoadOperation();
    });

    //注册delete事件，以便识别页面调用
    $bus.on('DeletePhoto', (photo) => {
      console.log('bus event');

      return;
      PhotoDelete(photo);
    });

    $bus.on('UploadReceiptForm', (files) => {
      receiptForm.value = files;
    });
  });
  onUnmounted(() => {
    $bus.off('EditOperation');
    $bus.off('DeletePhoto');
    $bus.off('UploadReceiptForm');
  });

  onBeforeMount(() => {
    operationid = '';
  });

  const CheckValidate = async () => {
    isShowLoading.value = true;
    loadingText.value = '加载中';
    try {
      const res = await formRef.value.validate();
      if (
        (Dapplication.value.patientName != formInfo.value.patientName ||
          Dapplication.value.patientGender != formInfo.value.patientGender ||
          Dapplication.value.implantDate != formInfo.value.implantDate ||
          Dapplication.value.implantHospitalCode != formInfo.value.implantHospitalCode ||
          Dapplication.value.implantHospital != formInfo.value.implantHospital) &&
        (formInfo.value.messageNote == '' ||
          formInfo.value.messageNote == undefined ||
          formInfo.value.messageNote.trim().length == 0) &&
        relatedSystemInfo.value
      ) {
        isShowLoading.value = false;
        isShowNoteLoading.value = true;
        return;
      }
      formInfo.value.details = products.value;
      formInfo.value.receiptForm = [];
      formInfo.value.authorizationLetter = [];
      if (
        formInfo.value.implantType == '仅替换导线' &&
        products.value.length > 0 &&
        products.value.some((item) => item.bigType === '主机')
      ) {
        isShowLoading.value = false;
        showFailToast({
          message: '[仅替换导线]不能有主机',
          duration: 3000,
        });
        return;
      }
      if (
        formInfo.value.implantType != undefined &&
        formInfo.value.implantType != '仅替换导线' &&
        products.value.length > 0 &&
        !products.value.some((item) => item.bigType === '主机')
      ) {
        isShowLoading.value = false;
        showFailToast({
          message: '[' + formInfo.value.implantType + ']必须有主机',
          duration: 3000,
        });
        return;
      }
      if (
        (authorizationLetter.value == undefined || authorizationLetter.value.length == 0) &&
        !relatedSystemInfo.value
      ) {
        isShowLoading.value = false;
        showFailToast({
          message: '请上传授权书',
          duration: 3000,
        });
        return;
      }
      if (receiptForm.value == undefined || receiptForm.value.length == 0) {
        isShowLoading.value = false;
        showFailToast({
          message: '请上传回执单',
          duration: 3000,
        });
        return;
      }
      formInfo.value.email = userInfo.value.email;
      if(operationid&&operationid!=""&&operationid!=undefined){
        formInfo.value.id = operationid;
      } 
      try{
        // **🚀 步骤1: 先执行 CkeckOperation**
        const checkRes = await request({
          method: 'post',
          url: '/api/epdt/CkeckOperation',
          data: formInfo.value,
        });

        if (checkRes.data) {
          tip = checkRes.data; // 处理校验结果
        }

        // **🚀 步骤2: 再执行 EditOperation**
        const editRes = await request({
          method: 'post',
          url: '/api/epdt/EditOperation',
          data: formInfo.value,
        });

        operationid = editRes.data;

        // **🚀 步骤3: 继续执行 uploadphoto**
        await uploadphoto(operationid);

        // **保存成功，触发事件**
        $bus.emit('EditOperation', '保存成功');
        $bus.emit('Todolist', '保存成功');
        showToast({ message: '保存成功', duration: 1000 });

        onPreviewOrValidate(); // 触发预览或验证
      }catch(error){
        isShowLoading.value = false;
        const errorMessage = error.response?.data || error.message || '操作失败';
        showFailToast({ message: errorMessage, duration: 3000 });
      }      
      // //跟台数据校验
      // request({
      //   method: 'post',
      //   url: '/api/epdt/CkeckOperation',
      //   data: formInfo.value,
      // })
      //   .then((checkres) => {
      //     //tip = "产品【测试】不在有效期";
      //     if (checkres.data) tip = checkres.data;
      //     formInfo.value.email = userInfo.value.email;
      //     request({
      //       method: 'post',
      //       url: '/api/epdt/EditOperation',
      //       data: formInfo.value,
      //     })
      //       .then((res) => {
      //         operationid = res.data;
      //         //uploadphoto();
      //         $bus.emit('EditOperation', '保存成功');
      //         $bus.emit('Todolist', '保存成功');
      //         showToast({
      //           message: '保存成功',
      //           duration: 1000,
      //         });
      //         onPreviewOrValidate();
      //         //router.back();
      //       })
      //       .catch((error) => {
      //         isShowLoading.value = false;
      //         showFailToast({
      //           message: error.response.data,
      //           duration: 3000,
      //         });
      //       });
      //   })
      //   .catch((error) => {
      //     isShowLoading.value = false;
      //     showFailToast({
      //       message: error.response.data,
      //       duration: 3000,
      //       style: {
      //         width: '150px', 
      //         textAlign: 'center'
      //       }
      //     });
      //   });
    } catch (err) {
      isShowLoading.value = false;
      basicValidateFailed(err);    
    }
  };

  const onPreviewOrValidate = async () => {
    try {
      await formRef.value.validate();
      request({
        url: 'api/epdt/GetCardTypes?operationid=' + operationid,
        method: 'get',
      })
        .then((res) => {
          isShowLoading.value = false;
          showDialog({
            message: '数据校验通过，识别卡预览！',
            title: '提示',
            closeOnClickOverlay: true,
            confirmButtonText: '识别卡预览',
          }).then(() => {
            console.log(res.data);
            router.push({
              name: 'previewcard',
              query: {
                operationid: operationid,
                Tip: tip,
                CardTypes: JSON.stringify(res.data),
                Approvalstatus: formInfo.value.onepdt_approval_statuslabel
                  ? formInfo.value.onepdt_approval_statuslabel
                  : '',
              },
            });
          });
        })
        .catch((error) => {
          isShowLoading.value = false;
          showFailToast({
            message: error.response.data,
            duration: 3000,
          });
        });
    } catch (err) {
      isShowLoading.value = false;
      basicValidateFailed(err);
    }
  };

  const onPreview = async () => {
    try {
      await formRef.value.validate();
      request({
        url: 'api/epdt/GetCardTypes?operationid=' + operationid,
        method: 'get',
      })
        .then((res) => {
          router.push({
            name: 'previewcard',
            query: {
              operationid: operationid,
              previewtype: '预览',
              CardTypes: JSON.stringify(res.data),
              Approvalstatus: formInfo.value.onepdt_approval_statuslabel
                ? formInfo.value.onepdt_approval_statuslabel
                : '',
            },
          });
        })
        .catch((error) => {
          showFailToast({
            message: error.response.data,
            duration: 3000,
          });
        });
    } catch (err) {
      basicValidateFailed(err);
      ProductSectionRef.value.scrollIntoView({
        behavior: 'smooth', // 平滑滚动
        block: 'start', // 滚动到元素顶部
      });
    }
  };

  const handleBeforeDelete = async () => {
    try {
      await showDialog({ message: '确认要删除吗？', showCancelButton: true });

      return true;
    } catch (error) {
      if (isVantDialogCancel(error)) {
        return false;
      }
      console.error(error);

      return false;
    }
  };

  return () => (
    <div class="flex-1 pt relative bg-mainbg" key="edit">
      <Header></Header>
      <div class="mt-4">
        <Form ref={formRef}>
          <Section title="手术信息" ref={userSectionRef} style="box-shadow: 0 0 0 1px #333;">
            <div class="">
              <Field
                border
                is-link={!Dapplicationisloading.value}
                label="关联患者注册信息"
                labelWidth="100%"
                input-align="left"
                type="textarea"
                autosize
                rows="2"
                v-model={relatedSystemInfo.value}
                readonly
                disabled={isdisabeld}
                onClick={() => {
                  if(!Dapplicationisloading.value){
                    if (!isdisabeld) {
                    select.updateSelect({
                      newSelectColumns: Dapplications.map((item, index) => ({
                        text: `${item.patientName}-${item.patientGender}-${item.implantDate}-${item.hospital}`,
                        value: item.id,
                      })),
                      newOnSelectConfirm: (option) => {
                        relatedSystemInfo.value = option.selectedOptions[0].text;
                        formInfo.value.deviceapplicationid = option.selectedOptions[0].value;
                        const selectedAssociatedSystemInformation = Dapplications.find(
                          (app) => app.id === option.selectedOptions[0].value
                        );
                        const {
                          patientGender,
                          patientName,
                          implantDate,
                          hospital,
                          hospitalcode,
                          hospitalid,
                          recipientname,
                          recipientphone,
                          mailingaddress,
                          epdt_if_need_paper_card,
                          epdt_card_acquisition_method,
                        } = selectedAssociatedSystemInformation;
                        formInfo.value.patientName = patientName;
                        formInfo.value.patientGender = patientGender === '男' ? '0' : '1';
                        formInfo.value.implantDate = implantDate;
                        formInfo.value.implantHospital = hospital;
                        formInfo.value.implantHospitalCode = hospitalcode;
                        formInfo.value.implantHospitalId = hospitalid;

                        Dapplication.value.patientName = patientName;
                        Dapplication.value.patientGender = patientGender === '男' ? '0' : '1';
                        Dapplication.value.implantDate = implantDate;
                        Dapplication.value.implantHospital = hospital;
                        Dapplication.value.implantHospitalCode = hospitalcode;
                        Dapplication.value.implantHospitalId = hospitalid;

                        if(epdt_if_need_paper_card == true){
                          if (epdt_card_acquisition_method == '医院获取') {
                            receType.value = '植入医院';
                            formInfo.value.receType = '0';
                            formInfo.value.rece = userInfo.value.name;
                            formInfo.value.tel = userInfo.value.phone;
                          } else {
                            receType.value = '自定义';
                            formInfo.value.receType = '1';
                            formInfo.value.address = mailingaddress;
                            formInfo.value.rece = recipientname;
                            formInfo.value.tel = recipientphone;
                          }
                        }
                        

                        isWarrantyCardMailed.value = epdt_if_need_paper_card;
                        formInfo.value.isWarrantyCardMailed = epdt_if_need_paper_card;
                        LoadHcp1List();
                        LoadHcp2List();
                        LoadHcp3List();
                      },
                      newIsShowSearch: true,
                    });
                    // isShowSelect.value = true;
                  }
                  }                  
                }}
              >
                {{
                  button: () =>
                    formInfo.value.deviceapplicationid && !isdisabeld ? (
                      <div
                        style="cursor: pointer; color: #999; padding: 0 10px;"
                        onClick={(e) => {
                          e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                          relatedSystemInfo.value = '';
                          formInfo.value.deviceapplicationid = undefined;
                          formInfo.value.patientName = '';
                          formInfo.value.patientGender = '';
                          formInfo.value.implantDate = '';
                          formInfo.value.implantHospital = '';
                          formInfo.value.implantHospitalCode = '';
                          formInfo.value.implantHospitalId = '';
                          Dapplication.value.patientName = '';
                          Dapplication.value.patientGender = '';
                          Dapplication.value.implantDate = '';
                          Dapplication.value.implantHospital = '';
                          Dapplication.value.implantHospitalCode = '';
                          Dapplication.value.implantHospitalId = '';
                          receType.value = '';
                          formInfo.value.receType = '';
                          formInfo.value.address = '';
                          formInfo.value.rece = '';
                          formInfo.value.tel = '';
                          isWarrantyCardMailed.value = false;
                          formInfo.value.isWarrantyCardMailed = false;
                        }}
                      >
                        ✖
                      </div>
                    ) : null,
                }}
              </Field>
              {Dapplicationisloading.value?(<Loading style="margin-right:15px" type="spinner" color="#1989fa" size="16" />):""}
              <Field
                border
                //required={operationid != ''}
                disabled={
                  isdisabeld &&
                  !(
                    formInfo.value.onepdt_submit_status_label == '跟台' &&
                    formInfo.value.onepdt_approval_status === null &&
                    formInfo.value.onepdt_address_approval_status === 0 &&
                    formInfo.value.onepdt_is_returned == true
                  )
                }
                id="patientName"
                name="patientName"
                input-align="right"
                errorMessageAlign="right"
                autocomplete="new-password"
                placeholder="请输入患者姓名"
                label="患者姓名"
                v-model={formInfo.value.patientName}
                //rules={[{ required: operationid != '', message: '请输入患者姓名' }]}
              ></Field>
              <Field
                border
                //   is-link
                label="患者性别"
                disabled={
                  isdisabeld &&
                  !(
                    formInfo.value.onepdt_submit_status_label == '跟台' &&
                    formInfo.value.onepdt_approval_status === null &&
                    formInfo.value.onepdt_address_approval_status === 0 &&
                    formInfo.value.onepdt_is_returned == true
                  )
                }
                //required={operationid != ''}
                errorMessageAlign="right"
                v-model={formInfo.value.patientGender}
                v-slots={{
                  input: () => (
                    <RadioGroup
                      direction="horizontal"
                      disabled={
                        isdisabeld &&
                        !(
                          formInfo.value.onepdt_submit_status_label == '跟台' &&
                          formInfo.value.onepdt_approval_status === null &&
                          formInfo.value.onepdt_address_approval_status === 0 &&
                          formInfo.value.onepdt_is_returned == true
                        )
                      }
                      v-model={formInfo.value.patientGender}
                      iconSize={16}
                    >
                      <Radio name="0">男</Radio>
                      <Radio name="1">女</Radio>
                    </RadioGroup>
                  ),
                }}
              ></Field>
              <Field
                border
                //   is-link
                id="hospital"
                name="hospital"
                autocomplete="new-password"
                label="植入医院"
                disabled={isdisabeld}
                readonly
                required
                errorMessageAlign="right"
                input-align="left"
                rules={[{ required: true, message: '请选择植入医院' }]}
                modelValue={formInfo.value.implantHospital}
                isLink
                onClick={() => {
                  if (!isdisabeld) {
                    select.updateSelect({
                      newSelectColumns: HospitalInfo.map((hospital, index) => ({
                        text: `${hospital.name}-${hospital.code}`,
                        value: hospital.id,
                      })),
                      newOnSelectConfirm: (option) => {
                        const hos = HospitalInfo.find((h) => h.id === option.selectedOptions[0].value);
                        formInfo.value.implantHospital = hos.name;
                        formInfo.value.implantHospitalCode = hos.code;
                        formInfo.value.implantHospitalId = hos.id;
                        formInfo.value.firstSurgeon = '';
                        formInfo.value.firstSurgeonid = '';
                        formInfo.value.secondSurgeon = '';
                        formInfo.value.secondSurgeonid = '';
                        formInfo.value.thirdSurgeon = '';
                        formInfo.value.thirdSurgeonid = '';
                        LoadHcp1List();
                        LoadHcp2List();
                        LoadHcp3List();
                      },
                      newIsShowSearch: true,
                      newSelectProps: {
                        // defaultValue: formInfo.value.implantHospital ? undefined : [HospitalInfo[2].id],
                      } as any,
                    });
                  }
                }}
              >
                {{
                  button: () =>
                    formInfo.value.implantHospital && !isdisabeld ? (
                      <div
                        style="cursor: pointer; color: #999; padding: 0 10px;"
                        onClick={(e) => {
                          e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                          formInfo.value.implantHospital = ''; // 清空内容
                          formInfo.value.implantHospitalCode = '';
                          formInfo.value.implantHospitalId = '';
                          formInfo.value.firstSurgeon = '';
                          formInfo.value.firstSurgeonid = '';
                          formInfo.value.secondSurgeon = '';
                          formInfo.value.secondSurgeonid = '';
                          formInfo.value.thirdSurgeon = '';
                          formInfo.value.thirdSurgeonid = '';
                        }}
                      >
                        ✖
                      </div>
                    ) : null,
                }}
              </Field>

              {}
              <Field
                border
                labelClass="text-gray-400"
                valueClass="text-gray-400"
                class="text-gray-400"
                id="hospitalCode"
                name="hospitalCode"
                autocomplete="new-password"
                label="植入医院代码"
                readonly
                errorMessageAlign="right"
                input-align="right"
                // modelValue={formInfo.value.implantHospitalCode}
                v-slots={{
                  input: () => <span class="text-gray-400">{formInfo.value.implantHospitalCode}</span>,
                }}
              ></Field>
              <Field
                border
                is-link
                label="植入时间"
                disabled={isdisabeld}
                id="implantDate"
                name="implantDate"
                required
                errorMessageAlign="right"
                input-align="right"
                rules={[{ required: true, message: '请选择植入时间' }]}
                // v-model={implantDate.value}
                v-model={formInfo.value.implantDate}
                readonly
                onClick={() => {
                  if (!isdisabeld) {
                    isShowCalendar.value = true;
                  }
                }}
              >
                {{
                  button: () =>
                    formInfo.value.implantDate && !isdisabeld ? (
                      <div
                        style="cursor: pointer; color: #999; padding: 0 10px;"
                        onClick={(e) => {
                          e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                          formInfo.value.implantDate = undefined; // 清空内容
                        }}
                      >
                        ✖
                      </div>
                    ) : null,
                }}
              </Field>

              <Cell class="p-0">
                <div class="flex" style="align-items: center;">
                  <Field
                    class="flex-1"
                    border
                    is-link={!firstSurgeonisloading.value}
                    label="第一术者"
                    disabled={
                      isdisabeld &&
                      !(
                        formInfo.value.onepdt_submit_status_label == '跟台' &&
                        formInfo.value.onepdt_approval_status === null &&
                        formInfo.value.onepdt_address_approval_status === 0 &&
                        formInfo.value.onepdt_is_returned == true
                      )
                    }
                    id="firstSurgeon"
                    name="firstSurgeon"
                    //required={operationid != ''}
                    errorMessageAlign="right"
                    input-align="right"
                    v-model={formInfo.value.firstSurgeon}
                    readonly
                    //rules={[{ required: operationid != '', message: '请输入第一术者' }]}
                    onClick={() => {
                      if(!firstSurgeonisloading.value){
                        if (
                        !isdisabeld ||
                        (formInfo.value.onepdt_submit_status_label == '跟台' &&
                          formInfo.value.onepdt_approval_status === null &&
                          formInfo.value.onepdt_address_approval_status === 0 &&
                          formInfo.value.onepdt_is_returned == true)
                      ) {
                        if (
                          formInfo.value.implantHospital === undefined ||
                          formInfo.value.implantHospital.trim() === ''
                        ) {
                          showToast('请选择植入医院');
                          return;
                        }
                        select.updateSelect({
                          newSelectColumns: hcp1.map((item, index) => ({
                            text: `${item.name}-${item.department}-${item.hospital}`,
                            value: item.id,
                            // className: 'justify-normal pl-[15vw]',
                          })),
                          newOnSelectConfirm: (option) => {
                            const h = hcp1.find((h) => h.id === option.selectedOptions[0].value);
                            if (
                              formInfo.value.secondSurgeon &&
                              formInfo.value.secondSurgeonid &&
                              formInfo.value.secondSurgeonid == h.id
                            ) {
                              showToast('第一术者与第二术者重复');
                              return;
                            }
                            if (
                              formInfo.value.thirdSurgeon &&
                              formInfo.value.thirdSurgeonid &&
                              formInfo.value.thirdSurgeonid == h.id
                            ) {
                              showToast('第一术者与第三术者重复');
                              return;
                            }
                            formInfo.value.firstSurgeon = h.name;
                            formInfo.value.firstSurgeonid = h.id;
                          },
                          newIsShowSearch: true,
                          // newSelectProps: {},
                        });
                      }
                      }                      
                    }}
                  >
                    {{
                      button: () =>
                        formInfo.value.firstSurgeonid && (
                          !isdisabeld ||
                          (formInfo.value.onepdt_submit_status_label == '跟台' &&
                            formInfo.value.onepdt_approval_status === null &&
                            formInfo.value.onepdt_address_approval_status === 0 &&
                            formInfo.value.onepdt_is_returned == true)
                        ) ? (
                          <div
                            style="cursor: pointer; color: #999; padding: 0 10px;"
                            onClick={(e) => {
                              e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                              formInfo.value.firstSurgeon = '';
                              formInfo.value.firstSurgeonid = '';
                            }}
                          >
                            ✖
                          </div>
                        ) : null,
                    }}
                    
                  </Field>
                   {firstSurgeonisloading.value?(<Loading style="margin-right:15px" type="spinner" color="#1989fa" size="16" />):""}
                  <Checkbox
                    iconSize={14}
                    disabled={
                      isdisabeld &&
                      !(
                        formInfo.value.onepdt_submit_status_label == '跟台' &&
                        formInfo.value.onepdt_approval_status === null &&
                        formInfo.value.onepdt_address_approval_status === 0 &&
                        formInfo.value.onepdt_is_returned == true
                      )
                    }
                    shape="square"
                    class="ml-auto pr-4"
                    label-position="left"
                    v-model={isExternalDoctor1.value}
                    onClick={() => {
                      formInfo.value.firstSurgeon = '';
                      formInfo.value.firstSurgeonid = '';
                      LoadHcp1List();
                    }}
                  >
                    <div>外院</div>
                  </Checkbox>
                </div>
              </Cell>
              <Cell class="p-0">
                <div class="flex" style="align-items: center;">
                  <Field
                    class="flex-1"
                    border={false}
                    is-link={!secondSurgeonisloading.value}
                    label="第二术者"
                    input-align="right"
                    name="secondSurgeon"
                    disabled={
                      isdisabeld &&
                      !(
                        formInfo.value.onepdt_submit_status_label == '跟台' &&
                        formInfo.value.onepdt_approval_status === null &&
                        formInfo.value.onepdt_address_approval_status === 0 &&
                        formInfo.value.onepdt_is_returned == true
                      )
                    }
                    id="secondSurgeon"
                    v-model={formInfo.value.secondSurgeon}
                    readonly
                    onClick={() => {
                      if(!secondSurgeonisloading.value){
                        if (
                        !isdisabeld ||
                        (formInfo.value.onepdt_submit_status_label == '跟台' &&
                          formInfo.value.onepdt_approval_status === null &&
                          formInfo.value.onepdt_address_approval_status === 0 &&
                          formInfo.value.onepdt_is_returned == true)
                      ) {
                        if (
                          formInfo.value.implantHospital === undefined ||
                          formInfo.value.implantHospital.trim() === ''
                        ) {
                          showToast('请选择植入医院');
                          return;
                        }
                        select.updateSelect({
                          newSelectColumns: hcp2.map((item, index) => ({
                            text: `${item.name}-${item.department}-${item.hospital}`,
                            value: item.id,
                          })),
                          newOnSelectConfirm: (option) => {
                            const h = hcp2.find((h) => h.id === option.selectedOptions[0].value);
                            if (
                              formInfo.value.firstSurgeon &&
                              formInfo.value.firstSurgeonid &&
                              formInfo.value.firstSurgeonid == h.id
                            ) {
                              showToast('第一术者与第二术者重复');
                              return;
                            }
                            if (
                              formInfo.value.thirdSurgeon &&
                              formInfo.value.thirdSurgeonid &&
                              formInfo.value.thirdSurgeonid == h.id
                            ) {
                              showToast('第三术者与第二术者重复');
                              return;
                            }
                            formInfo.value.secondSurgeon = h.name;
                            formInfo.value.secondSurgeonid = h.id;
                          },
                          newIsShowSearch: true,
                        });
                      }
                      }
                      
                    }}
                  >
                    {{
                      button: () =>
                        formInfo.value.secondSurgeonid && (
                          !isdisabeld ||
                          (formInfo.value.onepdt_submit_status_label == '跟台' &&
                            formInfo.value.onepdt_approval_status === null &&
                            formInfo.value.onepdt_address_approval_status === 0 &&
                            formInfo.value.onepdt_is_returned == true)
                        ) ? (
                          <div
                            style="cursor: pointer; color: #999; padding: 0 10px;"
                            onClick={(e) => {
                              e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                              formInfo.value.secondSurgeon = '';
                              formInfo.value.secondSurgeonid = '';
                            }}
                          >
                            ✖
                          </div>
                        ) : null,
                    }}
                  </Field>
                  {secondSurgeonisloading.value?(<Loading style="margin-right:15px" type="spinner" color="#1989fa" size="16" />):""}
                  <Checkbox
                    iconSize={14}
                    disabled={
                      isdisabeld &&
                      !(
                        formInfo.value.onepdt_submit_status_label == '跟台' &&
                        formInfo.value.onepdt_approval_status === null &&
                        formInfo.value.onepdt_address_approval_status === 0 &&
                        formInfo.value.onepdt_is_returned == true
                      )
                    }
                    shape="square"
                    class="ml-auto pr-4"
                    label-position="left"
                    v-model={isExternalDoctor2.value}
                    onClick={() => {
                      formInfo.value.secondSurgeon = '';
                      formInfo.value.secondSurgeonid = '';
                      LoadHcp2List();
                    }}
                  >
                    <div>外院</div>
                  </Checkbox>
                </div>
              </Cell>
              <Cell class="p-0">
                <div class="flex" style="align-items: center;">
                  <Field
                    class="flex-1"
                    border={false}
                    is-link={!thirdSurgeonisloading.value}
                    label="第三术者"
                    input-align="right"
                    name="thirdSurgeon"
                    disabled={
                      isdisabeld &&
                      !(
                        formInfo.value.onepdt_submit_status_label == '跟台' &&
                        formInfo.value.onepdt_approval_status === null &&
                        formInfo.value.onepdt_address_approval_status === 0 &&
                        formInfo.value.onepdt_is_returned == true
                      )
                    }
                    id="thirdSurgeon"
                    v-model={formInfo.value.thirdSurgeon}
                    readonly
                    onClick={() => {
                      if(!thirdSurgeonisloading.value){
                        if (
                        !isdisabeld ||
                        (formInfo.value.onepdt_submit_status_label == '跟台' &&
                          formInfo.value.onepdt_approval_status === null &&
                          formInfo.value.onepdt_address_approval_status === 0 &&
                          formInfo.value.onepdt_is_returned == true)
                      ) {
                        if (
                          formInfo.value.implantHospital === undefined ||
                          formInfo.value.implantHospital.trim() === ''
                        ) {
                          showToast('请选择植入医院');
                          return;
                        }
                        select.updateSelect({
                          newSelectColumns: hcp3.map((item, index) => ({
                            text: `${item.name}-${item.department}-${item.hospital}`,
                            value: item.id,
                          })),
                          newOnSelectConfirm: (option) => {
                            const h = hcp3.find((h) => h.id === option.selectedOptions[0].value);
                            if (
                              formInfo.value.firstSurgeon &&
                              formInfo.value.firstSurgeonid &&
                              formInfo.value.firstSurgeonid == h.id
                            ) {
                              showToast('第一术者与第三术者重复');
                              return;
                            }
                            if (
                              formInfo.value.secondSurgeon &&
                              formInfo.value.secondSurgeonid &&
                              formInfo.value.secondSurgeonid == h.id
                            ) {
                              showToast('第二术者与第三术者重复');
                              return;
                            }
                            formInfo.value.thirdSurgeon = h.name;
                            formInfo.value.thirdSurgeonid = h.id;
                          },
                          newIsShowSearch: true,
                        });
                      }
                      }                      
                    }}
                  >
                    {{
                      button: () =>
                        formInfo.value.thirdSurgeonid && (
                          !isdisabeld ||
                          (formInfo.value.onepdt_submit_status_label == '跟台' &&
                            formInfo.value.onepdt_approval_status === null &&
                            formInfo.value.onepdt_address_approval_status === 0 &&
                            formInfo.value.onepdt_is_returned == true)
                        ) ? (
                          <div
                            style="cursor: pointer; color: #999; padding: 0 10px;"
                            onClick={(e) => {
                              e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                              formInfo.value.thirdSurgeon = '';
                              formInfo.value.thirdSurgeonid = '';
                            }}
                          >
                            ✖
                          </div>
                        ) : null,
                    }}
                  </Field>
                  {thirdSurgeonisloading.value?(<Loading style="margin-right:15px" type="spinner" color="#1989fa" size="16" />):""}
                  <Checkbox
                    iconSize={14}
                    disabled={
                      isdisabeld &&
                      !(
                        formInfo.value.onepdt_submit_status_label == '跟台' &&
                        formInfo.value.onepdt_approval_status === null &&
                        formInfo.value.onepdt_address_approval_status === 0 &&
                        formInfo.value.onepdt_is_returned == true
                      )
                    }
                    shape="square"
                    class="ml-auto pr-4"
                    label-position="left"
                    v-model={isExternalDoctor3.value}
                    onClick={() => {
                      formInfo.value.thirdSurgeon = '';
                      formInfo.value.thirdSurgeonid = '';
                      LoadHcp3List();
                    }}
                  >
                    <div>外院</div>
                  </Checkbox>
                </div>
                <div class="px-4 text-left text-xs">如术者不在下拉列表中，请联系后台管理员添加</div>
              </Cell>

              <Cell class=" p-0">
                <div class="flex">
                  <Field
                    // class="w-auto  text-red-500"
                    class="w-[30ch]  text-red-500"
                    disabled={isdisabeld}
                    border
                    is-link
                    labelWidth="auto"
                    labelClass="w-[8ch] mr-4"
                    name="surgicalAssistantType"
                    id="surgicalAssistantType"
                    label="跟台者"
                    //required={operationid != ''}
                    errorMessageAlign="right"
                    input-align="right"
                    v-model={formInfo.value.surgicalAssistantType}
                    //rules={[{ required: operationid != '', message: '请选择跟台者' }]}
                    readonly
                    onClick={() => {
                      if (!isdisabeld) {
                        select.updateSelect({
                          newSelectColumns: GentaiType,
                          newOnSelectConfirm: (option) => {
                            if (option.selectedOptions[0].text === '仅本人跟台') {
                              formInfo.value.surgicalAssistant = userInfo.value.name;
                            } else {
                              formInfo.value.surgicalAssistant = '';
                            }
                            formInfo.value.surgicalAssistantType = option.selectedOptions[0].text;
                            formInfo.value.surgicalAssistantTypeid = option.selectedOptions[0].value;
                          },
                        });
                      }
                    }}
                    // }
                  >
                    {{
                      button: () =>
                        formInfo.value.surgicalAssistantTypeid && !isdisabeld ? (
                          <div
                            style="cursor: pointer; color: #999; padding: 0 10px;"
                            onClick={(e) => {
                              e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                              formInfo.value.surgicalAssistant = '';
                              formInfo.value.surgicalAssistantType = '';
                              formInfo.value.surgicalAssistantTypeid = '';
                            }}
                          >
                            ✖
                          </div>
                        ) : null,
                    }}
                  </Field>
                  <div class="flex-1">
                    <Field
                      class="pl-1"
                      name="surgicalAssistant"
                      disabled={isdisabeld}
                      id="surgicalAssistant"
                      inputAlign="left"
                      v-model={formInfo.value.surgicalAssistant}
                      placeholder={formInfo.value.surgicalAssistantType == '无需跟台' ? '' : '请输入姓名'}
                      autocomplete="new-password"
                      errorMessageAlign="right"
                      readonly={
                        formInfo.value.surgicalAssistantType == '无需跟台' ||
                        formInfo.value.surgicalAssistantType == '仅本人跟台'
                      }
                    ></Field>
                  </div>
                </div>
              </Cell>

              <Field
                border
                is-link
                label="跟台耗时"
                disabled={isdisabeld}
                input-align="right"
                v-model={surgeonDuration.value}
                readonly
                onClick={() => {
                  if (!isdisabeld) {
                    select.updateSelect({
                      newSelectColumns: Gentai,
                      newOnSelectConfirm: (option) => {
                        surgeonDuration.value = option.selectedOptions[0].text;
                        formInfo.value.surgeonDurationid = option.selectedOptions[0].value;
                      },
                    });
                  }
                }}
              >
                {{
                  button: () =>
                    formInfo.value.surgeonDurationid && !isdisabeld ? (
                      <div
                        style="cursor: pointer; color: #999; padding: 0 10px;"
                        onClick={(e) => {
                          e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                          surgeonDuration.value = '';
                        formInfo.value.surgeonDurationid = '';
                        }}
                      >
                        ✖
                      </div>
                    ) : null,
                }}
              </Field>

              <Field
                border
                is-link
                label="是否尝试心室生理性起搏"
                //label="希普系统起搏"
                labelWidth="100%"
                input-align="left"
                disabled={isdisabeld}
                v-model={hisPurkinjeSystemPacing.value}
                readonly
                onClick={() => {
                  if (!isdisabeld) {
                    select.updateSelect({
                      newSelectColumns: Xipu,
                      newOnSelectConfirm: (option) => {
                        hisPurkinjeSystemPacing.value = option.selectedOptions[0].text;
                        formInfo.value.hisPurkinjeSystemPacingid = option.selectedOptions[0].value;
                        ftoolslist.value = [];
                        failed_tool.value = "";
                        formInfo.value.failed_tool_list = "";
                        success_tool.value = "";
                        formInfo.value.success_tool_list = "";
                      },
                    });
                  }
                }}
              >
                {{
                  button: () =>
                    formInfo.value.hisPurkinjeSystemPacingid && !isdisabeld ? (
                      <div
                        style="cursor: pointer; color: #999; padding: 0 10px;"
                        onClick={(e) => {
                          e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                          hisPurkinjeSystemPacing.value = '';
                          formInfo.value.hisPurkinjeSystemPacingid = '';
                          ftoolslist.value = [];
                          failed_tool.value = "";
                          formInfo.value.failed_tool_list = "";
                          success_tool.value = "";
                          formInfo.value.success_tool_list = "";
                        }}
                      >
                        ✖
                      </div>
                    ) : null,
                }}
              </Field>

              {hisPurkinjeSystemPacing.value != undefined &&
              hisPurkinjeSystemPacing.value != '' &&
              hisPurkinjeSystemPacing.value.indexOf('成功') != -1 ? (
                <Field
                border
                is-link
                label="成功植入使用的工具"
                labelWidth="100%"
                input-align="left"
                disabled={isdisabeld}
                v-model={success_tool.value}
                id="successtool"
                name="successtool"
                required={true}
                rules={[
                  {
                    required: true,
                    message: '请选择成功植入使用的工具',
                  },
                ]}
                readonly
                onClick={() => {
                  if (!isdisabeld) {
                    select.updateSelect({
                      newSelectColumns: succTools.value,//Tools.value,
                      newOnSelectConfirm: (option) => {
                        success_tool.value = option.selectedOptions[0].text;
                        formInfo.value.success_tool_list = option.selectedOptions[0].text;
                      },
                    });
                  }
                }}
              >
                {{
                  button: () =>
                    formInfo.value.success_tool_list && !isdisabeld ? (
                      <div
                        style="cursor: pointer; color: #999; padding: 0 10px;"
                        onClick={(e) => {
                          e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                          success_tool.value = '';
                          formInfo.value.success_tool_list = '';
                        }}
                      >
                        ✖
                      </div>
                    ) : null,
                }}
              </Field>
              ) : (
                ''
              )}

              {hisPurkinjeSystemPacing.value != undefined &&
              hisPurkinjeSystemPacing.value != '' &&
              hisPurkinjeSystemPacing.value.indexOf('否') == -1 ? (
                <MoreSelect 
                ref={moreselectref}
                isclick={!isdisabeld}
                feildname="尝试工具" 
                checkedvalue={ftoolslist&&ftoolslist.value&&ftoolslist.value.length>0?ref(ftoolslist.value.map(o=>o.text)):null}
                feildvalue={failed_tool.value}
                //basicDiseaseColumns={ref(success_tool.value?Tools.value.filter(a=>a.text!==success_tool.value).map(o=>o.text):Tools.value.map(o=>o.text))}  
                basicDiseaseColumns={moreselecttools}
                onReturnData={(moredata) => {
                  console.log("尝试工具",moredata.value)
                    failed_tool.value = moredata.value;
                    formInfo.value.failed_tool_list = moredata.value;
                }}></MoreSelect>
              ) : (
                ''
              )}

              <Field
                border
                is-link
                label="是否尝试心房生理性起搏"
                labelWidth="100%"
                input-align="left"
                disabled={isdisabeld}
                v-model={xfsystem.value}
                readonly
                onClick={() => {
                  if (!isdisabeld) {
                    select.updateSelect({
                      newSelectColumns: Xinfang,
                      newOnSelectConfirm: (option) => {
                        xfsystem.value = option.selectedOptions[0].text;
                        formInfo.value.xfsystemId = option.selectedOptions[0].value;
                      },
                    });
                  }
                }}
              >
                {{
                  button: () =>
                    formInfo.value.xfsystemId && !isdisabeld ? (
                      <div
                        style="cursor: pointer; color: #999; padding: 0 10px;"
                        onClick={(e) => {
                          e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                          xfsystem.value = '';
                          formInfo.value.xfsystemId = '';
                        }}
                      >
                        ✖
                      </div>
                    ) : null,
                }}
              </Field>

              {/* {hisPurkinjeSystemPacing.value != undefined &&
              hisPurkinjeSystemPacing.value != '' &&
              hisPurkinjeSystemPacing.value != '否' ? (
                <Field
                  border
                  name="ksheath"
                  id="ksheath"
                  label="使用K鞘"
                  input-align="right"
                  disabled={isdisabeld}
                  readonly
                  v-slots={{
                    input: () => (
                      <Checkbox
                        iconSize={16}
                        disabled={isdisabeld}
                        shape="square"
                        modelValue={ksheath.value}
                        onClick={() => {
                          if (!isdisabeld) {
                            ksheath.value = !ksheath.value;
                            formInfo.value.ksheathvalue = ksheath.value?'1':'0';
                            formInfo.value.ksheathlabel = ksheath.value?'是':'否';
                          }
                        }}
                      ></Checkbox>
                    ),
                  }}
                ></Field>
              ) : (
                ''
              )} */}

              <Field
                border
                is-link
                labelClass="w-auto"
                label="ICD手术预防等级"
                disabled={isdisabeld}
                input-align="right"
                v-model={icdSurgeryProphylacticLevel.value}
                readonly
                onClick={() => {
                  if (!isdisabeld) {
                    select.updateSelect({
                      newSelectColumns: Icd,
                      newOnSelectConfirm: (option) => {
                        icdSurgeryProphylacticLevel.value = option.selectedOptions[0].text;
                        formInfo.value.icdSurgeryProphylacticLevelid = option.selectedOptions[0].value;
                      },
                    });
                  }
                }}
              >
                {{
                  button: () =>
                    formInfo.value.icdSurgeryProphylacticLevelid && !isdisabeld ? (
                      <div
                        style="cursor: pointer; color: #999; padding: 0 10px;"
                        onClick={(e) => {
                          e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                          icdSurgeryProphylacticLevel.value = '';
                          formInfo.value.icdSurgeryProphylacticLevelid = '';
                        }}
                      >
                        ✖
                      </div>
                    ) : null,
                }}
              </Field>
              <Field
                border
                // is-link
                label="ICD患者是否有冠脉病史"
                disabled={isdisabeld}
                input-align="right"
                labelClass="w-auto"
                readonly
                v-slots={{
                  input: () => (
                    <Checkbox
                      iconSize={16}
                      disabled={isdisabeld}
                      shape="square"
                      modelValue={isIcdPatientWithCadHistory.value}
                      onClick={() => {
                        if (!isdisabeld) {
                          isIcdPatientWithCadHistory.value = !isIcdPatientWithCadHistory.value;
                          formInfo.value.isIcdPatientWithCadHistory = isIcdPatientWithCadHistory.value;
                        }
                      }}
                    ></Checkbox>
                  ),
                }}
              ></Field>
            </div>
          </Section>
          <div class="py-2"></div>
          <Section title="上传图片" ref={implantSectionRef} style="box-shadow: 0 0 0 1px #333;">
            {/* <div class="flex w-full mb-4">
              <Button onClick={onReidentifyClick} disabled={isdisabeld} class="ml-auto mr-4" size="small" type="danger">
                识别/重置 回执单
              </Button>
            </div> */}
            <div>
            <Field
                border
                //   is-link
                //required={!relatedSystemInfo.value}
                label="授权书"
                // rules={[
                //   {
                //     required: !relatedSystemInfo.value,
                //     message: '请上传授权书',
                //   },
                // ]}
                v-model:show={authorizationLetter.value}
                readonly
                disabled={isdisabeld}
                onClick={() => {}}
                v-slots={{
                  input: () => (
                    <div>
                      <Uploader
                        multiple
                        maxSize={10*1024*1024}
                        onOversize={onOversize}
                        maxCount={2}
                        disabled={isdisabeld || !isedit}
                        v-model={authorizationLetter.value}
                        deletable={!isdisabeld && isedit}
                        onDelete={PhotoDelete}
                      ></Uploader>
                      {/* <Uploader></Uploader> */}
                    </div>
                  ),
                  extra: () =>
                    authorizationLetterloading.value ? (
                      <Loading
                        vertical
                        color="#1989fa"
                        size="42px"
                        style="position: absolute;  right: 30%;  top: 50%;  transform: translateY(-50%);"
                      >加载中...</Loading>
                    ) : null,
                }}
              ></Field>
              <Field
                border
                //   is-link
                rules={[{ required: false, message: '请上传回执单' }]}
                label="回执单"
                disabled={isdisabeld}
                v-model:show={receiptForm.value}
                readonly
                onClick={() => {
                  // isShowBarcodeScanPage.value = true;
                  // console.log('照片', receiptForm.value);
                }}
                v-slots={{
                  input: () => (
                    <div>
                      <Uploader
                        disabled={isdisabeld || !isedit}
                        deletable={!isdisabeld && isedit}
                        maxSize={10*1024*1024}
                        onOversize={onOversize}
                        readonly
                        maxCount={2}
                        beforeDelete={handleBeforeDelete}
                        onClick={() => {
                          if (!isdisabeld && isedit) {
                            receiptFormStore.updateReceiptForm({
                              fileData: receiptForm.value,
                              option: { isDisabled: isdisabeld },
                              barcodeArr: [],
                              action: ACTION.SEND_TO_BARCODE,
                            });
                            // debugger
                            router.push({ name: 'barcodescan' });
                          }
                        }}
                        previewFullImage={isdisabeld}
                        // previewImage={false}
                        multiple
                        onDelete={PhotoDelete}
                        v-model={receiptForm.value}
                      ></Uploader>
                    </div>
                  ),
                  extra: () =>
                    receiptFormloading.value ? (
                      <Loading
                        vertical
                        color="#1989fa"
                        size="42px"
                        style="position: absolute;  right: 30%;  top: 50%;  transform: translateY(-50%);"
                      >加载中...</Loading>
                    ) : null,
                }}
              ></Field>
              <div ref={ProductSectionRef}></div>              
            </div>
          </Section>

          <div class="py-2"></div>
          <Section title="植入产品" badgeCount={products.value.length} style="box-shadow: 0 0 0 1px #333;">
            <Field
                border
                name="implantType"
                is-link
                label="植入类型"
                disabled={isdisabeld}
                required
                rules={[{ required: true, message: '请输入植入类型' }]}
                labelClass="w-auto"
                input-align="right"
                v-model={implantType.value}
                readonly
                onClick={() => {
                  if (!isdisabeld) {
                    select.updateSelect({
                      newSelectColumns: zhirutype,
                      newOnSelectConfirm: (option) => {
                        implantType.value = option.selectedOptions[0].text;
                        formInfo.value.implantTypeid = option.selectedOptions[0].value;
                        formInfo.value.implantType = option.selectedOptions[0].text;
                        //if(implantType.value === 'Aveir DR部分更换VR/AR'||implantType.value === 'Aveir VR/AR升级为DR'){
                        //  GetHistoryOperation();
                        //}
                      },
                    });
                  }
                }}
              >
                {{
                  button: () =>
                    formInfo.value.implantTypeid && !isdisabeld ? (
                      <div
                        style="cursor: pointer; color: #999; padding: 0 10px;"
                        onClick={(e) => {
                          e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                          implantType.value = '';
                          formInfo.value.implantTypeid = '';
                          formInfo.value.implantType = '';
                        }}
                      >
                        ✖
                      </div>
                    ) : null,
                }}
              </Field>

              {implantType.value === '仅替换导线' && (
                <Field
                  border
                  is-link
                  label="现使用主机类型"
                  disabled={isdisabeld}
                  name="mainUnitType"
                  id="mainUnitType"
                  required={implantType.value === '仅替换导线'}
                  rules={[{ required: implantType.value === '仅替换导线', message: '请选择现使用主机类型' }]}
                  labelClass="w-auto"
                  input-align="right"
                  v-model={mainUnitType.value}
                  readonly
                  onClick={() => {
                    if (!isdisabeld) {
                      select.updateSelect({
                        newSelectColumns: UnitType,
                        newOnSelectConfirm: (option) => {
                          mainUnitType.value = option.selectedOptions[0].text;
                          formInfo.value.replaceddeviceid = option.selectedOptions[0].value;
                        },
                      });
                      // isShowSelect.value = true;
                    }
                  }}
                >
                  {{
                    button: () =>
                      formInfo.value.replaceddeviceid && !isdisabeld ? (
                        <div
                          style="cursor: pointer; color: #999; padding: 0 10px;"
                          onClick={(e) => {
                            e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                            mainUnitType.value = '';
                            formInfo.value.replaceddeviceid = '';
                          }}
                        >
                          ✖
                        </div>
                      ) : null,
                  }}
                </Field>
              )}

              {implantType.value === '整体更换主机' && (
                <Field
                  border
                  is-link
                  label="原主机类型"
                  disabled={isdisabeld}
                  name="originalMainUnitType"
                  id="originalMainUnitType"
                  required={implantType.value === '整体更换主机'}
                  rules={[{ required: implantType.value === '整体更换主机', message: '请选择原主机类型' }]}
                  input-align="right"
                  v-model={originalMainUnitType.value}
                  readonly
                  onClick={() => {
                    if (!isdisabeld) {
                      select.updateSelect({
                        newSelectColumns: UnitType,
                        newOnSelectConfirm: (option) => {
                          originalMainUnitType.value = option.selectedOptions[0].text;
                          formInfo.value.originaldeviceid = option.selectedOptions[0].value;
                        },
                      });
                      // isShowSelect.value = true;
                    }
                  }}
                >
                  {{
                    button: () =>
                      formInfo.value.originaldeviceid && !isdisabeld ? (
                        <div
                          style="cursor: pointer; color: #999; padding: 0 10px;"
                          onClick={(e) => {
                            e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                            originalMainUnitType.value = '';
                            formInfo.value.originaldeviceid = '';
                          }}
                        >
                          ✖
                        </div>
                      ) : null,
                  }}
                </Field>
              )}

              {implantType.value === 'Aveir DR部分更换VR/AR'||implantType.value === 'Aveir VR/AR升级为DR'?
              (<Field
                border
                is-link
                label="历史跟台"
                labelWidth="100%"
                name="relatedOperation"
                id="relatedOperation"
                input-align="left"
                v-model={relatedOperation.value}
                readonly
                type="textarea"
                autosize
                rows="2"
                disabled={isdisabeld}
                onClick={() => {
                  if (!isdisabeld) {
                    select.updateSelect({
                      newSelectColumns: HistoryOperation.map((item, index) => ({
                        text: `${item.onepdt_patient}-${item.onepdt_date}-${item.onepdt_product_sn}`,
                        value: item.id,
                      })),
                      newOnSelectConfirm: (option) => {
                        const hosop = HistoryOperation.find((h) => h.id === option.selectedOptions[0].value);
                        formInfo.value.relatedoperationid = option.selectedOptions[0].value;
                        relatedOperation.value = `${hosop.onepdt_patient}-${hosop.onepdt_date}-${hosop.onepdt_hospitalname}-${hosop.onepdt_product_name}-${hosop.onepdt_product_sn}`
                      },
                      newIsShowSearch: true,
                    });
                    // isShowSelect.value = true;
                  }
                }}
              >
                {{
                  button: () =>
                    formInfo.value.relatedoperationid && !isdisabeld ? (
                      <div
                        style="cursor: pointer; color: #999; padding: 0 10px;"
                        onClick={(e) => {
                          e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                          relatedOperation.value = '';
                          formInfo.value.relatedoperationid = '';
                        }}
                      >
                        ✖
                      </div>
                    ) : null,
                }}
              </Field>):""}
            <div class="flex flex-col gap-4" style="margin-top: 5px;">
              {products.value.map((product, index) => (
                <>
                  <ProductCard
                    key={product.randomId}
                    product={product}
                    disabled={isdisabeld || !isedit}
                    index={index}
                    onClose={(index) => {
                      products.value.splice(index, 1);
                    }}
                  ></ProductCard>
                </>
              ))}
            </div>

            <div class="m-4">
              <Button
                size="small"
                disabled={isdisabeld || !isedit}
                type="primary"
                class=""
                onClick={() => {
                  products.value.push({ bigType: '主机' });
                }}
              >
                新增产品
              </Button>
            </div>
            {/* <CellGroup inset>
            
            </CellGroup> */}
          </Section>

          <div class="py-2"></div>
          <Section title="邮寄信息" style="box-shadow: 0 0 0 1px #333;">
            <NoticeBar class="px-8" wrapable>
              邮寄信息会根据实际情况进行调整！
            </NoticeBar>
            <CellGroup inset>
              <Field
                label="是否需要实体卡"
                labelClass="w-auto"
                disabled={
                  ((formInfo.value.onepdt_address_approval_status === 0 ||
                    formInfo.value.onepdt_address_approval_status === 1) &&
                    formInfo.value.onepdt_approval_status === 1) ||
                  (formInfo.value.deviceapplicationid &&
                    formInfo.value.deviceapplicationid != undefined &&
                    !(formInfo.value.onepdt_address_approval_status === 2)) ||
                  (formInfo.value.onepdt_approval_status == null &&
                    formInfo.value.onepdt_address_approval_status == 0 &&
                    formInfo.value.onepdt_is_returned == true) || 
                  (formInfo.value.onepdt_approval_status == 0 && (
                    formInfo.value.onepdt_address_approval_status == 0 || 
                    formInfo.value.onepdt_address_approval_status == 1
                  ))
                }
                inputAlign="right"
                v-slots={{
                  input: () => (
                    <Checkbox
                      modelValue={isWarrantyCardMailed.value}
                      disabled={
                        ((formInfo.value.onepdt_address_approval_status === 0 ||
                          formInfo.value.onepdt_address_approval_status === 1) &&
                          formInfo.value.onepdt_approval_status === 1) ||
                        (formInfo.value.deviceapplicationid &&
                          formInfo.value.deviceapplicationid != undefined &&
                          !(formInfo.value.onepdt_address_approval_status === 2)) ||
                        (formInfo.value.onepdt_approval_status == null &&
                          formInfo.value.onepdt_address_approval_status == 0 &&
                          formInfo.value.onepdt_is_returned == true)|| 
                          (formInfo.value.onepdt_approval_status == 0 && (
                            formInfo.value.onepdt_address_approval_status == 0 || 
                            formInfo.value.onepdt_address_approval_status == 1
                          ))
                      }
                      onClick={() => {
                        if (
                          !(
                            ((formInfo.value.onepdt_address_approval_status === 0 ||
                              formInfo.value.onepdt_address_approval_status === 1) &&
                              formInfo.value.onepdt_approval_status === 1) ||
                            (formInfo.value.deviceapplicationid &&
                              formInfo.value.deviceapplicationid != undefined &&
                              !(formInfo.value.onepdt_address_approval_status === 2)) ||
                            (formInfo.value.onepdt_approval_status == null &&
                              formInfo.value.onepdt_address_approval_status == 0 &&
                              formInfo.value.onepdt_is_returned == true)|| 
                              (formInfo.value.onepdt_approval_status == 0 && (
                                formInfo.value.onepdt_address_approval_status == 0 || 
                                formInfo.value.onepdt_address_approval_status == 1
                              ))
                          )
                        ) {
                          isWarrantyCardMailed.value = !isWarrantyCardMailed.value;
                          formInfo.value.isWarrantyCardMailed = isWarrantyCardMailed.value;
                        }
                      }}
                      iconSize={16}
                      shape="square"
                    ></Checkbox>
                  ),
                }}
              />
              {isWarrantyCardMailed.value && (
                <>
                  <Field
                    v-model={receType.value}
                    // placeholder="请输入收件人"
                    isLink
                    readonly
                    name="receType"
                    id="receType"
                    required={isWarrantyCardMailed.value}
                    rules={[{ required: isWarrantyCardMailed.value, message: '请选择实体卡邮寄地址' }]}
                    labelClass="w-auto"
                    disabled={
                      ((formInfo.value.onepdt_address_approval_status === 0 ||
                        formInfo.value.onepdt_address_approval_status === 1) &&
                        formInfo.value.onepdt_approval_status === 1) ||
                      (formInfo.value.deviceapplicationid &&
                        formInfo.value.deviceapplicationid != undefined &&
                        !(formInfo.value.onepdt_address_approval_status === 2)) ||
                      (formInfo.value.onepdt_approval_status == null &&
                        formInfo.value.onepdt_address_approval_status == 0 &&
                        formInfo.value.onepdt_is_returned == true)|| 
                        (formInfo.value.onepdt_approval_status == 0 && (
                          formInfo.value.onepdt_address_approval_status == 0 || 
                          formInfo.value.onepdt_address_approval_status == 1
                        ))
                    }
                    inputAlign="right"
                    onClick={() => {
                      if (
                        !(
                          ((formInfo.value.onepdt_address_approval_status === 0 ||
                            formInfo.value.onepdt_address_approval_status === 1) &&
                            formInfo.value.onepdt_approval_status === 1) ||
                          (formInfo.value.deviceapplicationid &&
                            formInfo.value.deviceapplicationid != undefined &&
                            !(formInfo.value.onepdt_address_approval_status === 2)) ||
                          (formInfo.value.onepdt_approval_status == null &&
                            formInfo.value.onepdt_address_approval_status == 0 &&
                            formInfo.value.onepdt_is_returned == true) || 
                            (formInfo.value.onepdt_approval_status == 0 && (
                              formInfo.value.onepdt_address_approval_status == 0 || 
                              formInfo.value.onepdt_address_approval_status == 1
                            ))
                        )
                      ) {
                        select.updateSelect({
                          newSelectColumns: [
                            { text: '植入医院', value: '0' },
                            { text: '自定义', value: '1' },
                          ],
                          newOnSelectConfirm: (option) => {
                            receType.value = option.selectedOptions[0].text;
                            formInfo.value.receType = option.selectedOptions[0].value;
                            if (option.selectedOptions[0].text == '植入医院') {
                              formInfo.value.rece = userInfo.value.name;
                              formInfo.value.tel = userInfo.value.phone;
                            }
                          },
                        });
                      }
                    }}
                    label="实体卡邮寄地址">
                    {{
                      button: () =>
                        (
                          formInfo.value.receType &&
                          !(
                            ((formInfo.value.onepdt_address_approval_status === 0 ||
                              formInfo.value.onepdt_address_approval_status === 1) &&
                              formInfo.value.onepdt_approval_status === 1) ||
                            (formInfo.value.deviceapplicationid &&
                              formInfo.value.deviceapplicationid != undefined &&
                              !(formInfo.value.onepdt_address_approval_status === 2)) ||
                            (formInfo.value.onepdt_approval_status == null &&
                              formInfo.value.onepdt_address_approval_status == 0 &&
                              formInfo.value.onepdt_is_returned == true) || 
                              (formInfo.value.onepdt_approval_status == 0 && (
                                formInfo.value.onepdt_address_approval_status == 0 || 
                                formInfo.value.onepdt_address_approval_status == 1
                              ))
                          )
                        ) ? (
                          <div
                            style="cursor: pointer; color: #999; padding: 0 10px;"
                            onClick={(e) => {
                              e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                              receType.value = '';
                              formInfo.value.receType = '';
                              formInfo.value.rece = '';
                              formInfo.value.tel = '';
                              formInfo.value.address = '';
                            }}
                          >
                            ✖
                          </div>
                        ) : null,
                    }}
                  </Field>

                  <Field
                    v-model={formInfo.value.address}
                    required={receType.value == '自定义'}
                    rows="3"
                    type="textarea"
                    name="address"
                    id="address"
                    disabled={
                      ((formInfo.value.onepdt_address_approval_status === 0 ||
                        formInfo.value.onepdt_address_approval_status === 1) &&
                        formInfo.value.onepdt_approval_status === 1) ||
                      (formInfo.value.deviceapplicationid &&
                        formInfo.value.deviceapplicationid != undefined &&
                        !(formInfo.value.onepdt_address_approval_status === 2)) ||
                        receType.value == '植入医院' ||
                      (formInfo.value.onepdt_approval_status == null &&
                        formInfo.value.onepdt_address_approval_status == 0 &&
                        formInfo.value.onepdt_is_returned == true) || 
                        (formInfo.value.onepdt_approval_status == 0 && (
                          formInfo.value.onepdt_address_approval_status == 0 || 
                          formInfo.value.onepdt_address_approval_status == 1
                        ))
                    }
                    rules={[{ required: receType.value == '自定义', message: '请输入邮寄地址' }]}
                    readonly={
                      ((formInfo.value.onepdt_address_approval_status === 0 ||
                        formInfo.value.onepdt_address_approval_status === 1) &&
                        formInfo.value.onepdt_approval_status === 1) ||
                      (formInfo.value.deviceapplicationid &&
                        formInfo.value.deviceapplicationid != undefined &&
                        !(formInfo.value.onepdt_address_approval_status === 2)) ||
                        receType.value == '植入医院' ||
                      (formInfo.value.onepdt_approval_status == null &&
                        formInfo.value.onepdt_address_approval_status == 0 &&
                        formInfo.value.onepdt_is_returned == true) || 
                        (formInfo.value.onepdt_approval_status == 0 && (
                          formInfo.value.onepdt_address_approval_status == 0 || 
                          formInfo.value.onepdt_address_approval_status == 1
                        ))
                    }
                    placeholder="请输入邮寄地址"
                    inputAlign="left"
                    label="邮寄地址"
                  />
                  <Field
                    v-model={formInfo.value.rece}
                    placeholder="请输入收件人"
                    name="rece"
                    id="rece"
                    required={isWarrantyCardMailed.value}
                    rules={[{ required: isWarrantyCardMailed.value, message: '请输入收件人' }]}
                    disabled={
                      ((formInfo.value.onepdt_address_approval_status === 0 ||
                        formInfo.value.onepdt_address_approval_status === 1) &&
                        formInfo.value.onepdt_approval_status === 1) ||
                      (formInfo.value.deviceapplicationid &&
                        formInfo.value.deviceapplicationid != undefined &&
                        !(formInfo.value.onepdt_address_approval_status === 2)) ||
                      receType.value == '植入医院' ||
                      (formInfo.value.onepdt_approval_status == null &&
                        formInfo.value.onepdt_address_approval_status == 0 &&
                        formInfo.value.onepdt_is_returned == true) || 
                        (formInfo.value.onepdt_approval_status == 0 && (
                          formInfo.value.onepdt_address_approval_status == 0 || 
                          formInfo.value.onepdt_address_approval_status == 1
                        ))
                    }
                    readonly={((formInfo.value.onepdt_address_approval_status === 0 ||
                      formInfo.value.onepdt_address_approval_status === 1) &&
                      formInfo.value.onepdt_approval_status === 1) ||
                    (formInfo.value.deviceapplicationid &&
                      formInfo.value.deviceapplicationid != undefined &&
                      !(formInfo.value.onepdt_address_approval_status === 2)) ||
                    receType.value == '植入医院' ||
                    (formInfo.value.onepdt_approval_status == null &&
                      formInfo.value.onepdt_address_approval_status == 0 &&
                      formInfo.value.onepdt_is_returned == true) || 
                      (formInfo.value.onepdt_approval_status == 0 && (
                        formInfo.value.onepdt_address_approval_status == 0 || 
                        formInfo.value.onepdt_address_approval_status == 1
                      ))}
                    inputAlign="right"
                    label="收件人姓名"
                  />
                  <Field
                    v-model={formInfo.value.tel}
                    placeholder="请输入联系电话"
                    name="tel"
                    id="tel"
                    rows="2"
                    type="textarea"
                    required={isWarrantyCardMailed.value}
                    rules={[{ required: isWarrantyCardMailed.value, message: '请输入联系电话' }]}
                    disabled={
                       ((formInfo.value.onepdt_address_approval_status === 0 ||
                         formInfo.value.onepdt_address_approval_status === 1) &&
                         formInfo.value.onepdt_approval_status === 1) ||
                       (formInfo.value.deviceapplicationid &&
                         formInfo.value.deviceapplicationid != undefined &&
                         !(formInfo.value.onepdt_address_approval_status === 2)) ||
                       receType.value == '植入医院' ||
                       (formInfo.value.onepdt_approval_status == null &&
                         formInfo.value.onepdt_address_approval_status == 0 &&
                         formInfo.value.onepdt_is_returned == true) || 
                         (formInfo.value.onepdt_approval_status == 0 && (
                           formInfo.value.onepdt_address_approval_status == 0 || 
                           formInfo.value.onepdt_address_approval_status == 1
                         ))
                     }
                    readonly={((formInfo.value.onepdt_address_approval_status === 0 ||
                      formInfo.value.onepdt_address_approval_status === 1) &&
                      formInfo.value.onepdt_approval_status === 1) ||
                    (formInfo.value.deviceapplicationid &&
                      formInfo.value.deviceapplicationid != undefined &&
                      !(formInfo.value.onepdt_address_approval_status === 2)) ||
                    receType.value == '植入医院' ||
                    (formInfo.value.onepdt_approval_status == null &&
                      formInfo.value.onepdt_address_approval_status == 0 &&
                      formInfo.value.onepdt_is_returned == true) || 
                      (formInfo.value.onepdt_approval_status == 0 && (
                        formInfo.value.onepdt_address_approval_status == 0 || 
                        formInfo.value.onepdt_address_approval_status == 1
                      ))}
                    inputAlign="right"
                    label="联系电话"
                  />
                </>
              )}

              {/* <Field
                v-model={formInfo.value.messageNote}
                rows="2"
                autosize
                inputAlign="right"
                disabled={
                  isdisabeld &&
                  !(
                    formInfo.value.onepdt_submit_status_label == '跟台' &&
                    formInfo.value.onepdt_approval_status === null &&
                    formInfo.value.onepdt_address_approval_status === 0 &&
                    formInfo.value.onepdt_is_returned == true
                  ) &&
                  !(formInfo.value.onepdt_address_approval_status === 2)
                }
                label="备注"
                type="textarea"
                maxlength="100"
                placeholder="请输入备注"
                show-word-limit
              /> */}
            </CellGroup>
          </Section>
          <div class="py-2"></div>
          <Section title="备注信息" style="box-shadow: 0 0 0 1px #333;">
            <CellGroup inset>
              <Field
                v-model={formInfo.value.messageNote}
                rows="4"
                autosize
                inputAlign="left"
                disabled={
                  isdisabeld &&
                  !(
                    formInfo.value.onepdt_submit_status_label == '跟台' &&
                    formInfo.value.onepdt_approval_status === null &&
                    formInfo.value.onepdt_address_approval_status === 0 &&
                    formInfo.value.onepdt_is_returned == true
                  ) &&
                  !(formInfo.value.onepdt_address_approval_status === 2)
                }
                label="备注"
                labelWidth="100%"
                type="textarea"
                maxlength="100"
                placeholder="请输入备注"
                show-word-limit
              />
            </CellGroup>
          </Section>
          <div class="pt-4  sticky bottom-0 w-full">
            {formInfo.value.onepdt_address_approval_status == 2? (
              <div>
                {/* <NoticeBar class="px-8" wrapable background="var(--abbott-red)" color="white" style="height: fit-content">
                标签：{formInfo.value.onepdt_address_approval_tag}
              </NoticeBar> */}
                <NoticeBar
                  class="px-8"
                  wrapable
                  background="var(--abbott-red)"
                  color="white"
                  style="height: fit-content"
                >
                  退回原因：{formInfo.value.onepdt_address_approval_comment};
                  {formInfo.value.onepdt_address_approval_tag}
                </NoticeBar>
              </div>
            ):""}
            {(formInfo.value.onepdt_approval_status == 2 || 
            (formInfo.value.onepdt_approval_status == null && formInfo.value.onepdt_address_approval_status == 0 && formInfo.value.onepdt_is_returned == true) ||
            (formInfo.value.onepdt_submit_status_label == "周报" && formInfo.value.onepdt_address_approval_status == null && formInfo.value.onepdt_approval_status == null && formInfo.value.onepdt_is_returned == true)) ? (
              <div>
                {/* <NoticeBar class="px-8" wrapable background="var(--abbott-red)" color="white" style="height: fit-content">
                标签：{formInfo.value.onepdt_approval_tag}
              </NoticeBar> */}
                <NoticeBar
                  class="px-8"
                  wrapable
                  background="var(--abbott-red)"
                  color="white"
                  style="height: fit-content"
                >
                  退回原因：{formInfo.value.onepdt_approval_comment};{formInfo.value.onepdt_approval_tag}
                </NoticeBar>
              </div>
            ):""}
            <div class=" py-2 px-6   bg-white w-full    flex justify-center items-center gap-4">
              {operationid != '' &&
              isedit &&
              operationid != undefined &&
              formInfo.value.onepdt_submit_status_label == '周报' &&
              formInfo.value.onepdt_approval_status !== 1 ? (
                <Button class="flex-1 " onClick={debounce(onDeleteDraft)} type="danger">
                  删除
                </Button>
              ) : (
                ''
              )}

              {/* {operationid == '' ||operationid == undefined || operationid == null ? (
                <Button class="flex-1 " onClick={debounce(ClearOperationInfo)} type="danger">
                  清除
                </Button>
              ) : (
                ''
              )} */}

              {(operationid == '' ||
              operationid == undefined ||
              (formInfo.value.onepdt_submit_status_label == '周报' && formInfo.value.onepdt_approval_status !== 1) && isedit)? (
                <Button class="flex-1 " onClick={debounce(onConfirmSaveAsDraft)}>
                  {operationid && operationid != undefined && operationid != null && operationid != ''
                    ? '保存'
                    : '生成周报'}
                </Button>
              ) : (
                ''
              )}

              {formInfo.value.onepdt_submit_status_label == '跟台' ? (
                <Button class="flex-1 " type="primary" onClick={debounce(onPreview)}>
                  预览
                </Button>
              ) : (
                ''
              )}

              {formInfo.value.onepdt_submit_status_label != '跟台' && isedit ? (
                <Button class="flex-1 " type="primary" onClick={debounce(CheckValidate)}>
                  校验
                </Button>
              ) : (
                ''
              )}

              {formInfo.value.onepdt_address_approval_status === 2 ||
              (formInfo.value.onepdt_submit_status_label == '跟台' &&
                formInfo.value.onepdt_approval_status === null &&
                formInfo.value.onepdt_address_approval_status === 0 &&
                formInfo.value.onepdt_is_returned == true &&
                isedit) ? (
                <Button class="flex-1 " type="primary" onClick={debounce(SubmitOperation)}>
                  提交
                </Button>
              ) : (
                ''
              )}
            </div>
          </div>
        </Form>
      </div>

      {/* <Select></Select> */}
      <Calendar
        v-model:show={isShowCalendar.value}
        onConfirm={onConfirmImplantDate}
        switch-mode="year-month"
        minDate={new Date('1995-01-01')}
        maxDate={new Date()}
      ></Calendar>

      {/* <Dialog v-model:show={isShowLoading.value} showConfirmButton={false}>
        <div class="flex flex-col justify-center items-center p-2 h-40">
          <Loading></Loading>
          {loadingText.value}
        </div>
      </Dialog> */}

      <Dialog
        v-model:show={isShowNoteLoading.value}
        showConfirmButton={true}
        showCancelButton={true}
        title="请输入修改关联信息原因"
        onConfirm={() => {
          const regex = /^[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]+$/;
          if (
            formInfo.value.messageNote &&
            (formInfo.value.messageNote.trim().length == 0 || regex.test(formInfo.value.messageNote))
          ) {
            formInfo.value.messageNote = '';
          }
        }}
        onCancel={() => {
          formInfo.value.messageNote = '';
        }}
      >
        <Field
          v-model={formInfo.value.messageNote}
          rows="2"
          autosize
          inputAlign="right"
          label="备注"
          type="textarea"
          maxlength="100"
          placeholder="请输入"
          show-word-limit
        />
      </Dialog>
    </div>
  );
};

export default defineComponent(Edit);
