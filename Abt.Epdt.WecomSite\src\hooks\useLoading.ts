import { createApp, createVNode, onMounted, ref, Ref, watch, render } from 'vue';
import Loading from '../components/Loading';

const useLoading = (isShowLoading: Ref<Boolean>, message?: string) => {
  const component = ref();
  const element = ref();

  watch(
    () => isShowLoading.value,
    (newValue) => {
      if (newValue) {
        // const appWithProps = createApp(Loading, { message });
        const appWithProps = createVNode(Loading, { message });
        component.value = appWithProps;
        const loadingElement = document.getElementById('loading');
        if (document.getElementById('loading')) {
          element.value = loadingElement;
          //   document.getElementById('loading')?.remove();
        } else {
          const containerWithProps = document.createElement('div');
          element.value = containerWithProps;
          containerWithProps.id = 'loading';
        }

        document.body.appendChild(element.value);
        render(appWithProps, element.value);
      } else {
        render(null, element.value);
      }
    }
  );
};

export default useLoading;
