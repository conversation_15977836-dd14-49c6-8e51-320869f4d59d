import { Button, Cell, CellGroup } from "vant";
import { defineComponent, FunctionalComponent } from "vue";
import { RouterLink, useRouter } from "vue-router";

type Hospital = { name: string; code: number };
type ListProps = {
  hospitalList: Hospital[];
};
const List: FunctionalComponent<ListProps> = (props) => {
  const router = useRouter();
  // router.push('/')

  return () => (
    <div class="bg-white page">
      <div class="flex p-2">
        <div class="flex-1">医院</div>
        <div class="flex-1 text-right pr-6">编辑</div>
      </div>
      <div>
        <CellGroup>
          {props.hospitalList.map((hospital) => (
            <Cell title={hospital.name}>
              <div class="flex">
                {/* <div class="flex-1">{hospital.name}</div> */}
                <div class="flex-1">
                  {/* <RouterLink to="/">
                    <Button size="mini">用户管理</Button>
                  </RouterLink> */}
                  <RouterLink
                    to={{
                      name: "edit",
                      query: { name: hospital.name, code: hospital.code },
                    }}
                  >
                    {/* fill */}
                    <Button
                      size="mini"
                      onClick={() => {
                        console.log("click");
                        console.log(router.getRoutes().length);
                        // router.push("/edit")
                        // router.push({ name: "edit" });
                        console.log(router.getRoutes());
                      }}
                    >
                      数据填写
                    </Button>
                  </RouterLink>
                </div>
              </div>
            </Cell>
          ))}
        </CellGroup>
      </div>
    </div>
  );
};

export default defineComponent(List, { props: ["hospitalList"] });
// export default List
// export default defineComponent({
//     setup() {

//         return () => (<div>
//             hospital
//         </div>)
//     }
// })
