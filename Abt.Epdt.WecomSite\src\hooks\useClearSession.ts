import { onMounted, onUnmounted, ref } from 'vue';
import <PERSON><PERSON> from 'js-cookie';
import { showConfirmDialog } from 'vant';
import { getLoginUrl } from '#src/utils/auth.ts';

const activityEvents = ['mousemove', 'keydown', 'click'];

let clearSessionEvent;
let lastActivityTime = new Date().getTime();
// const timeoutTime = 1000 * 60 * 60 * 1;
const timeoutTime = 1000 * 60 * 60 * 1;
let timeoutTimer;
// const windowVisibility = ref(true);
const onvisibilityChange = (e) => {
  //   console.log(e.target.visibilityState);
  const state = e.target.visibilityState;
  if (state === 'hidden') {
    console.log('清空timer');
    clearTimeout(timeoutTimer);
  }
  if (state === 'visible') {
    console.log('设置timer');
    startTimeoutTimer();
  }
};

function resetTimeout() {
  lastActivityTime = new Date().getTime();
  console.log('start timeout');
  clearTimeout(timeoutTimer);
  startTimeoutTimer();
  console.log('用户活动，重置超时时间'); // 示例日志
}

function startTimeoutTimer() {
  timeoutTimer = setTimeout(checkTimeout, timeoutTime);
}

function checkTimeout() {
  const currentTime = new Date().getTime();
  const elapsedTime = currentTime - lastActivityTime;

  if (elapsedTime >= timeoutTime) {
    Cookie.remove('onepdtToken');
    showConfirmDialog({ title: '您已超出会话时间，请重新登录', showCancelButton: false }).then((_) => {
      const previousLoginEmail = localStorage.getItem('previousLoginEmail');
      const url = getLoginUrl(previousLoginEmail);
      location.href = url;
    });
    clearTimeout(timeoutTimer);
    // console.log('会话超时，执行退出'); // 示例日志
  } else {
    startTimeoutTimer();
  }
}

export const useClearSession = () => {
  onMounted(() => {
    // clearTimeout(clearSessionEvent);

    startTimeoutTimer();
    // clearSessionEvent = setTimeout(() => {
    //   Cookie.remove('onepdtToken');
    //   showConfirmDialog({ title: '您已超出会话时间，请重新登录', showCancelButton: false }).then((_) => {
    //     const url = getLoginUrl();
    //     location.href = url;
    //   });
    // }, 1000 * 60 * 60 * 1);

    activityEvents.forEach((event) => {
      document.addEventListener(event, resetTimeout);
    });
    document.addEventListener('visibilitychange', onvisibilityChange);
  });
  onUnmounted(() => {
    activityEvents.forEach((event) => {
      document.removeEventListener(event, resetTimeout);
    });
    document.removeEventListener('visibilitychange', onvisibilityChange);
  });
};
