# 简介
该项目是一个基于 Vue 3 + TypeScript + Vite 构建的企业微信跟台系统，以供销售用户在为企业微信端进入该系统进行跟台信息的填报。该项目使用现代前端技术栈，包括 Vant UI 组件库，提供了移动端友好的用户界面和交互体验。

# 入门

1. 安装过程
   - 克隆此仓库: `git clone 本仓库`
   - 进入项目目录: `cd Abt.Epdt.WecomSite`
   - 安装依赖项: `npm install`
   - 本地调试代码： `npm run dev:local`

2. 软件依赖项
   - Node.js 16.x 或更高版本
   - npm
   - Vite
   - Vant
   - Tailwind CSS
   - Vue
   - Vue Router

3. 最新发布
   - 检查项目的[源码仓库地址](https://git.oneabbott.com/BTS/CA183/_git/CA183-OnePDT-FE)以获取最新版本。



# 生成与测试
- 构建代码: `npm run build:prod`
