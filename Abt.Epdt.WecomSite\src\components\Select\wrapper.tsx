import { defineComponent, inject, onMounted, provide, ref, watchEffect, watch, onUpdated,onUnmounted } from 'vue';
import Select from '.';
import { Picker } from 'vant';
import { onBeforeRouteLeave, onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router';


const GlobalSelect = () => {
  const state = inject('select') as any;
  const selectValue = ref<string[]>([]);
  const route = useRoute();
  watch(() => route.fullPath, (newPath, oldPath) => {
    // 在这里处理路由变化的逻辑
    if(newPath!=oldPath){
      state.isShowSelect.value = false;
    }
  });
  onMounted(() => {
  });
  onUpdated(() => {
    if (state.selectProps?.value?.defaultValue) {
      selectValue.value = state.selectProps?.value.defaultValue;
    }
  });
  return () => (
    <Select
      selectProps={
        {
          onCancel: () => {
            state.isShowSelect.value = false;
            state.searchText.value = '';
          },
          columns: state.selectColumns.value.filter((item) => {
            if (state.searchText.value === '') return true;
            if (item.text.includes(state.searchText.value)) return true;
          }).slice(0,50),
          onConfirm: (option) => {
            state.onSelectConfirm.value(option);
            state.isShowSelect.value = false;
            state.searchText.value = '';
          },
          modelValue: selectValue.value,
        } as InstanceType<typeof Picker>['$props']
      }
      searchText={state.searchText}
      //   key={state.isShowSelect.value ? 1 : 2}
      state={state}
      isShowSearch={state.isShowSearch}
      isShowSelect={state.isShowSelect}
    ></Select>
  );
};

export default defineComponent(GlobalSelect);
