import { defineComponent, FunctionalComponent, onMounted, ref } from 'vue';
import { Cell, CellGroup, Image, showToast, showFailToast, Field, Popup, Button } from 'vant';
import { onBeforeRouteLeave, onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router';
import { useUserStore } from '#src/stores/index.js';
import { storeToRefs } from 'pinia';
import { useGlobalSelect } from '#src/hooks/useSelect.js';
import Header from '#src/components/Header/index';
import request from '#src/service/request.js';
import userAvatar from '#src/assets/userheader.png';

//角色
var Rolelist = [];
//是否展示切换邮箱
var isShowSwitch = ref(false);
var switchemail = ref('');

const Mine: FunctionalComponent = () => {
  const userStore = useUserStore();
  const select = useGlobalSelect();
  const userInfo = storeToRefs(userStore);
  const router = useRouter();
  onMounted(() => {
    //LoginAndGetRole(false);
    switchemail.value = userInfo.userInfo.value.email;
    // console.log(userInfo.userInfo.value.role);
    // debugger
  });
  const handleRoleChange = () => {
    console.log('查询用户角色', userInfo.userInfo.value.Rolelist);
    select.updateSelect({
      newSelectColumns: userInfo.userInfo.value.Rolelist.map((item, index) => ({
        text: item.rolename,
        value: item.roleid,
        seq: item.roleseq,
        roleid: item.roleid,
        istodo: item.istodo,
        isquery: item.isquery,
        isproxy: item.isproxy,
        proxyemail: item.proxyemail,
      })),
      newOnSelectConfirm: (e) => {
        userStore.updateUser({
          ...userInfo.userInfo.value,
          role: e.selectedOptions[0].text,
          roleid: e.selectedOptions[0].roleid,
          istodo: e.selectedOptions[0].istodo,
          isquery: e.selectedOptions[0].isquery,
          isproxy: e.selectedOptions[0].isproxy,
          proxyemail: e.selectedOptions[0].proxyemail,
        });
        SwitchRole(e.selectedOptions[0].roleid);
      },
    });
  };

  const SwitchRole = async (roleid) => {
    request({
      method: 'get',
      url: '/api/epdt/SwitchRole?userid=' + userInfo.userInfo.value.id + '&roleid=' + roleid,
    })
      .then((res) => {
        if (res.data) {
          userStore.updateUser({
            ...userInfo.userInfo.value,
            isexitstodolist: res.data,
          });
        }
        showToast('切换成功');
      })
      .catch((error) => {
        showFailToast({
          message: error.response.data,
          duration: 3000,
        });
      });
  };

  const goPreviewFile = () => {
    router.push({
      name: 'filepreview',
    });
  };

  const base64toFile = (dataurl, filename = 'file') => {
    const arr = dataurl.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const suffix = mime.split('/')[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], `${filename}.${suffix}`, {
      type: mime,
    });
  };

  //切换用户
  const switchuser = async () => {
    if (switchemail.value == null || switchemail.value == undefined || switchemail.value == '') {
      showFailToast({
        message: '请输出邮箱',
        duration: 2000,
      });
    } else {
      userStore.updateUser({
        ...userInfo.userInfo.value,
        email: switchemail.value,
      });

      LoginAndGetRole(true);
    }
    isShowSwitch.value = false;
  };

  //获取用户角色
  const LoginAndGetRole = async (isswitchuser) => {
    request({
      method: 'get',
      url: '/api/epdt/LoginAndGetRole?email=' + userInfo.userInfo.value.email,
    })
      .then((res) => {
        console.log('用户角色', res);
        Rolelist = [];
        if (res.data.role && res.data.role.length > 0) {
          Rolelist = res.data.role;
          console.log('roleid111', userInfo.userInfo.value.roleid);
          userStore.updateUser({
            ...userInfo.userInfo.value,
            name: res.data.username,
            id: res.data.userid,
            isexitstodolist: res.data.isexitstodolist,
            proxyemail: '',
          });
          console.log('roleid222', userInfo.userInfo.value.roleid);
          if (
            !userInfo.userInfo.value.roleid ||
            userInfo.userInfo.value.roleid == '' ||
            userInfo.userInfo.value.roleid == null ||
            userInfo.userInfo.value.roleid == undefined ||
            isswitchuser
          ) {
            userStore.updateUser({
              ...userInfo.userInfo.value,
              role: res.data.role[0].rolename,
              phone: res.data.role[0].phone,
              roleid: res.data.role[0].roleid,
              istodo: res.data.role[0].istodo,
              isquery: res.data.role[0].isquery,
              isproxy: res.data.role[0].isproxy,
              proxyemail: res.data.role[0].proxyemail,
            });
          }
          console.log('roleid333', userInfo.userInfo.value);
        }
      })
      .catch((error) => {
        showFailToast({
          message: error.response.data,
          duration: 3000,
        });
      });
  };
  return () => (
    <div class="flex flex-col h-full  w-full bg-white ">
      <Header
        isShowBack={true}
        onClickBack={() => {
          router.replace({ name: 'landingpage' });
        }}
      ></Header>
      <div class="flex pt-8 ml-8 gap-2">
        <Image
          fit="cover"
          width={60}
          height={60}
          src={userAvatar}
          onClick={() => {
            isShowSwitch.value = true;
          }}
          round
        ></Image>
        <div class="flex flex-col gap-2">
          <div>{userInfo.userInfo.value.name}</div>
          <div class="text-sm text-gray-400">{userInfo.userInfo.value.role}</div>
        </div>
      </div>

      <div class="w-full pt-8">
        <CellGroup border>
          <Cell onClick={handleRoleChange} title="切换角色" isLink value={userInfo.userInfo.value.role}></Cell>
        </CellGroup>
        <CellGroup border>
          <Cell title="使用说明" isLink onClick={goPreviewFile}></Cell>
        </CellGroup>
        {/* <embed src="http://localhost:3000/api/epdt/GetUserManual" type="application/pdf" /> */}
        {/* <iframe src="http://localhost:3000/api/epdt/GetUserManual" style="height:300px;width:300px"></iframe> */}
      </div>
      <Popup
        show={isShowSwitch.value}
        position="bottom"
        onClickOverlay={() => {
          isShowSwitch.value = false;
        }}
      >
        <div style="display:flex;">
          <Field label="切换用户邮箱" v-model={switchemail.value} placeholder="请输入邮箱"></Field>
          <Button
            style="width:80px"
            size="small"
            type="primary"
            onClick={() => {
              switchuser();
            }}
          >
            切换
          </Button>
        </div>
      </Popup>
    </div>
  );
};

export default defineComponent(Mine);
