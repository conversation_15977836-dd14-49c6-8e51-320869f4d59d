import { dotnet } from "./_framework/dotnet.js";
let exportsPromise = null;

const createRuntimeAndGetExports = async () => {
  const { getAssemblyExports, getConfig } = await dotnet.withResourceLoader((type, name, defaultUri, integrity) => {
    // 👇 Override extension of ICU files
    if (type == 'globalization') {
        defaultUri = defaultUri.replace('.dat', '.icu');
    }

    return defaultUri;
}).create();
  const config = getConfig();
  return await getAssemblyExports(config.mainAssemblyName);
};

export async function getEmployee() {
  if (!exportsPromise) {
    exportsPromise = createRuntimeAndGetExports();
  }
  const exports = await exportsPromise;
  return exports.JSBridge.GetEmployee();
}

export async function setEmployee(id, name, age, isActive) {
  if (!exportsPromise) {
    exportsPromise = createRuntimeAndGetExports();
  }
  const exports = await exportsPromise;
  return exports.JSBridge.SetEmployee(id, name, age, isActive);
}

export async function decodeImage(url) {
  if (!exportsPromise) {
    exportsPromise = createRuntimeAndGetExports();
  }
  const exports = await exportsPromise;
  return exports.JSBridge.DecodeImage(url);
}

export async function compressImage(url, maxWidth, quality) {
  if (!exportsPromise) {
    exportsPromise = createRuntimeAndGetExports();
  }
  const exports = await exportsPromise;
  return exports.JSBridge.Compress(url, maxWidth, quality);
}

export async function CollectGC() {
  if (!exportsPromise) {
    exportsPromise = createRuntimeAndGetExports();
  }
  const exports = await exportsPromise;
  return exports.JSBridge.CollectGC();
}


export async function decodeSingleImage(url) {
  if (!exportsPromise) {
    exportsPromise = createRuntimeAndGetExports();
  }
  const exports = await exportsPromise;
  // debugger
  return exports.JSBridge.DecodeSingleImage(url);
}
