import Header from '#src/components/Header';
import {
  Bad<PERSON>,
  Button,
  Checkbox,
  Dialog,
  Loading,
  Popup,
  showDialog,
  showFailToast,
  showImagePreview,
  showToast,
  Uploader,
  UploaderFileListItem,
  UploaderInstance,
  Image as VantImage,
} from 'vant';
import Compressor from 'compressorjs';

import {
  defineComponent,
  FunctionalComponent,
  getCurrentInstance,
  nextTick,
  onMounted,
  onUnmounted,
  ref,
  toRaw,
  watch,
  watchEffect,
} from 'vue';
import BarcodeResultCard from './BarcodeResultCard';
import './index.css';
import { useRoute, useRouter } from 'vue-router';
import { ACTION, useReceiptFormStore } from '#src/stores/receiptForm';
import { storeToRefs } from 'pinia';
import { isVantDialogCancel, TimeoutError } from '#src/utils/basic';
import $bus from '#src/mitt';
import request from '#src/service/request';
import barcodeUtils, { cropBarcodesFromImage, detectBarcodes, preHandleAndDecodeImage } from '#src/utils/barcode';
import utils from '#src/utils/barcode';
import { Jimp, JimpMime } from 'jimp';

const BarcodeScan: FunctionalComponent<{ closePopup; defaultValues }, { confirm; delete }> = (props, context) => {
  const files = ref<(UploaderFileListItem & { data?: any })[]>([]);
  const isShowLoading = ref(false);
  const loadingText = ref('');

  const receiptFormStore = useReceiptFormStore();
  const receiptFormForPageTransfer = storeToRefs(receiptFormStore);
  const isDisabled = ref(false);

  const router = useRouter();

  // const handleBeforeReadImage: (selectFiles: File) => Promise<File> = (selectedFiles) => {
  const handleBeforeReadImage = async (selectedFiles) => {
    isShowLoading.value = true;
    loadingText.value = '加载中';

    try {
      if (!Array.isArray(selectedFiles)) selectedFiles = [selectedFiles];
      if (Array.isArray(selectedFiles)) {
      }

      const getImgInfo = (url) => {
        console.log('get url');

        return new Promise((resolve, reject) => {
          let image = new Image();
          image.src = url;
          let timer = setInterval(() => {
            if (image.width > 0 || image.height > 0) {
              resolve(`${image.width}*${image.height}`); // 图片宽*高
              clearInterval(timer);
            }
          }, 50);
        });
      };
    } catch (error) {
      console.error(error);
    } finally {
      isShowLoading.value = false;
    }
    return selectedFiles;
  };

  const fileUploaderRef = ref<UploaderInstance>();

  const handleAddImage = (newFile: UploaderFileListItem) => {
    // console.log(file.value);
    files.value.push(newFile);
  };
  const handleDeleteOneFile = (file) => {
    $bus.emit('DeletePhoto', file);
  };

  const handleDeleteAllFile = async (files) => {
    const ids = files.filter((file) => file.id).map((file) => file.id);
    if (ids.length === 0) {
      return;
    }
    isShowLoading.value = true;
    loadingText.value = '删除中';

    request({
      url: 'api/epdt/BatchDelete',
      method: 'post',
      data: {
        entityname: 'onepdt_t_opreation_photo',
        entityids: ids,
      },
    })
      .then(() => {
        // $bus.emit('EditOperation', '删除图片成功');
        showToast({ message: '删除成功' });
      })
      .catch((error) => {
        showFailToast({
          message: error.response.data,
          duration: 3000,
        });
      })
      .finally(() => {
        isShowLoading.value = false;
      });
  };

  onMounted(() => {
    if (receiptFormForPageTransfer.receiptForm.value.action === ACTION.SEND_TO_BARCODE) {
      files.value = receiptFormForPageTransfer.receiptForm.value.fileData;

      receiptFormStore.updateReceiptForm({ fileData: [], barcodeArr: [], action: ACTION.UNDEFINED });

      isDisabled.value = receiptFormForPageTransfer.receiptForm.value.option?.isDisabled ?? false;
    } else if (receiptFormForPageTransfer.receiptForm.value.action === ACTION.UNDEFINED) {
      router.back();
    }
  });

  const decodeImage = async (url) => {
    url = await (
      await Jimp.read(url)
    )
      .greyscale() //去除颜色
      .normalize() //
      .brightness(1.5)
      .contrast(0.5)
      .threshold({ max: 128 })
      .posterize(2)
      .getBase64(JimpMime.png);

    const barcodePositionArr = await detectBarcodes(url);
    let barcodeImgArr = (await cropBarcodesFromImage(url, barcodePositionArr)) as {
      content: string;
      angle: number;
    }[];

    const resArr = await Promise.all(await preHandleAndDecodeImage(barcodeImgArr));

    return { code: resArr.filter((item) => item !== '') };
  };

  const handleDecodeAllImages = async () => {
    if (files.value.length === 0) {
      showToast('请添加回执单');
      return;
    }
    isShowLoading.value = true;
    loadingText.value = '识别中';

    await new Promise((resolve) => setTimeout(resolve, 100));

    // const timeout = 1000;
    const timeout = 3 * 60 * 1000; // 3 minutes in milliseconds
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new TimeoutError('识别超时，请刷新页面重试'));
      }, timeout);
    });

    try {
      const promiseArr = files.value.map((file) => decodeImage(file.content));
      const dataArr = await Promise.race([Promise.all(promiseArr), timeoutPromise]);

      //识别出至少一个条形码
      if (Array.isArray(dataArr) && dataArr.some((data) => data.code.length !== 0)) {
        for (let i = 0; i < dataArr.length; i++) {
          const data = dataArr[i];
          const fileIndex = files.value.findIndex(
            (curItem) =>
              curItem.file.name === files.value[i].file.name && curItem.file.size === files.value[i].file.size
          );
          files.value[fileIndex].file.data = data.code.length === 0 ? [] : data.code;
        }
        showToast('识别成功');
      } else {
        showToast('没有识别到条形码');
      }
    } catch (error) {
      console.error(error);
      if (error instanceof TimeoutError) {
        showToast('识别超时，请刷新页面重新执行');
      } else {
        showToast('识别错误');
      }
    } finally {
      isShowLoading.value = false;
      utils.wasm.CollectGC();
    }
  };

  const handleClearFiles = async () => {
    await handleDeleteAllFile(files.value);
    files.value = [];
    // showToast('清空成功');
  };

  const handleConfirm = async () => {
    //识别结果去重

    try {
      const codeSet = new Set<string>();
      files.value.forEach((file) => {
        if (file.file.data && Array.isArray(file.file.data)) {
          file.file.data.forEach((code) => {
            codeSet.add(barcodeUtils.extractSerialNumber(code));
          });
        }
      });
      const codeArr = Array.from(codeSet);

      await showDialog({
        message: () => (
          <div>
            <div>确认将产品信息清除并且将以下结果录入吗？</div>
            <div class="mt-4">
              {codeArr.length > 0 ? codeArr.map((code) => <div>{code}</div>) : <div>暂无结果</div>}
            </div>
          </div>
        ),
        showCancelButton: true,
      });
      receiptFormStore.updateReceiptForm({
        fileData: files.value,
        barcodeArr: Array.from(codeSet),
        action: ACTION.BACK_TO_EDIT,
      });
      router.back();
    } catch (error) {
      console.error(error);

      if (!isVantDialogCancel(error)) {
        showToast('发生错误');
      }
    }
  };
  const handleBack = () => {
    $bus.emit('UploadReceiptForm', files.value);
    router.back();
  };
  onUnmounted(() => {});

  const handleBeforeDelete = async () => {
    try {
      await showDialog({ message: '确认要删除吗？', showCancelButton: true });

      return true;
    } catch (error) {
      if (isVantDialogCancel(error)) {
        return false;
      }
      console.error(error);

      return false;
    }
  };
  const onOversize = async(photo) =>{
    console.log(photo);
    showFailToast({
      message: "图片不能超过10M",
      duration: 3000,
      style: { width: '150px', textAlign: 'center' },
    });
  };

  return () => {
    return (
      <div class="bg-mainbg h-screen max-h-screen flex flex-col  overflow-hidden barcode-scan">
        {/* <Header onClickBack={handleBack}></Header> */}
        <Header></Header>
        <div class="p-2 flex flex-col flex-1 gap-2 overflow-hidden bg-white ">
          <Dialog v-model:show={isShowLoading.value} showConfirmButton={false}>
            <div class="flex flex-col justify-center items-center p-2 h-40">
              <Loading></Loading>
              {loadingText.value}
            </div>
          </Dialog>
          <div class="flex gap-2 w-full  "></div>
          <div class="flex items-center ">
            <Uploader
              ref={fileUploaderRef}
              previewFullImage={false}
              maxSize={10*1024*1024}
              onOversize={onOversize}
              onClickPreview={(e) => {
                const startIndex = files.value.findIndex((file) => file.objectUrl === e.objectUrl);
                showImagePreview(
                  files.value.map((file) => file.content),
                  startIndex === -1 ? 1 : startIndex
                );
              }}
        
              beforeDelete={handleBeforeDelete}
              onDelete={handleDeleteOneFile}
              disabled={isDisabled.value}
              deletable={!isDisabled.value}
              v-model={files.value}
              multiple
              max-count="2"
              v-slots={{
                'preview-cover': (currnetFile) => {
                  return currnetFile.file.data && Array.isArray(currnetFile.file.data) ? (
                    <div class="relative m-1">
                      <Badge
                        class="!transform-none !top-0 !left-0 absolute !right-auto"
                        content={currnetFile.file.data.length}
                      ></Badge>
                    </div>
                  ) : (
                    <></>
                  );
                },
              }}
            ></Uploader>
            <Button class="ml-auto p-4" size="normal" type="primary" onClick={handleDecodeAllImages}>
              识别
            </Button>
          </div>

          <div class="flex-1 overflow-hidden ">
            <div class="max-h-full min-h-full  overflow-scroll px-2 text-black ">
              <div class=" font-bold text-lg">识别结果</div>
              <div class="flex flex-col gap-2 ">
                {files.value.map((file: any, index) => {
                  return <BarcodeResultCard name={`图片${index + 1}`} result={file.file.data}></BarcodeResultCard>;
                })}
              </div>
            </div>
          </div>

          <div class="mt-auto w-full  gap-2 flex-col">
            <div class="text-sm text-center  text-gray-500">确认按钮将会对结果去重</div>

            <div class="  w-full flex gap-2">
              <Button
                size="large"
                type="danger"
                onClick={() => {
                  handleClearFiles();
                }}
              >
                清空
              </Button>

              <Button
                size="large"
                type="primary"
                onClick={() => {
                  router.back();
                }}
              >
                返回
              </Button>
              <Button size="large" type="primary" onClick={handleConfirm}>
                确认
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  };
};

export default defineComponent(BarcodeScan, { props: ['closePopup', 'defaultValues'] });
