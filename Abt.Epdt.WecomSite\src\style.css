@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .page {
    height: 100%;
  }

  .electronic-warranty-card-title {
    @apply text-[var(--abbott-color)] text-sm;
  }
  .title {
    @apply font-bold w-full text-center text-lg;
  }
}

@font-face {
  /* font-family: abbott; */
  /* src: url('./assets/SimHei.ttf') */
  /* src: url('./assets/SimSun.ttf') */
}
body {
  /* font-family: abbott; */
}

:root {
  --van-cell-vertical-padding: 8px;
  /* --van-button-primary-background: var(--abbott-color);
  --van-button-primary-border-color: var(--abbott-color);
  --van-tag-primary-color: var(--abbott-color); */
  --van-primary-color: var(--abbott-color);
  --van-tag-primary-color: var(--abbott-medium-blue);
  --van-button-danger-background: var(--abbott-red);
  --swiper-theme-color: var(--abbott-color) !important;
}
#app {
  height: 100vh;
}

.epdt .p-0 {
  padding: 0;
}

.epdt .w-32 {
  width: 8rem;
}

.epdt .w-48 {
  width: 12rem;
}
.epdt .w-auto {
  width: auto;
}

.baseinfo-show {
  grid-template-rows: 1fr;
}

.baseinfo {
  transition: 0.3s;
  display: grid;
}

.baseinfo-hidden {
  grid-template-rows: 0fr;
}

:root {
  --abbott-color: #009cde;
  --abbott-medium-blue: #004f71;
  --abbott-red: #e4002b;
  --abbott-yellow: #ffd100;
  --abbott-orange: #ff6900;
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  /* color: rgba(255, 255, 255, 0.87); */
  /* background-color: #242424; */

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.van-picker-column__item--selected{
  @apply !text-abbott;
}
.van-picker-column__item{
  @apply text-gray-500 text-sm !text-center !w-full !px-2;
} 
/* .van-picker-column{
  position: relative;
}

.van-picker-column__wrapper{
  position: absolute !important;
  top: -40%  !important;
  width: 100%;
}

.van-picker__frame{
  top:10%;
}
.van-picker__mask{
                     background: none !important;
}
.van-picker-column__item--selected{
  @apply !text-abbott;
}
.van-picker-column__item{
  @apply text-gray-500 text-sm !text-center !w-full !px-2;
} */





html{
  color: black;
  /* height: 100vh; */
  /* overflow-y: auto; */
}
body {
  height: 100vh;
  /* overflow-y: auto; */
  /* height: 100%; */
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.1s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.2s ease;
}
.slide-enter,
.slide-leave-to {
  transform: translateX(100%);
}

.slide-left-enter {
  opacity: 0;
  -webkit-transform: translate(30px, 0);
  transform: translate(30px, 0);
}
.slide-left-enter-active {
  transition: all 0.2s ease;
}
.slide-left-leave-to {
  opacity: 0;
  -webkit-transform: translate(-30px, 0);
  transform: translate(-30px, 0);
}
.slide-left-leave-active {
  transition: all 0.2s ease;
}

/* 覆盖 van-toast 的宽度 */
.van-toast {
  width: 150px !important;  /* 设置宽度 */
  text-align: center;
}
