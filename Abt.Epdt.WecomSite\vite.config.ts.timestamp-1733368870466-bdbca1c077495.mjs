// vite.config.ts
import { defineConfig } from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/vite@5.4.11_@types+node@20.14.12/node_modules/vite/dist/node/index.js";
import vue from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/@vitejs+plugin-vue@5.0.5_vite@5.4.11_@types+node@20.14.12__vue@3.4.33_typescript@5.5.4_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.0.0_vite@5.4.11_@types+node@20.14.12__vue@3.4.33_typescript@5.5.4_/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import AutoImport from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/unplugin-auto-import@0.18.1_@nuxt+kit@3.14.159_rollup@4.27.3__rollup@4.27.3/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/unplugin-vue-components@0.27.4_@babel+parser@7.26.2_@nuxt+kit@3.14.159_rollup@4.27.3__rollup@_bfwdfymrtn6hergliv2lmc564u/node_modules/unplugin-vue-components/dist/vite.js";
import { VantResolver } from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/@vant+auto-import-resolver@1.2.1/node_modules/@vant/auto-import-resolver/dist/index.esm.mjs";

// src/plugins/copywasm.ts
import copy from "file:///C:/Users/<USER>/workspace/code/dotnet/abt-pp-epdt/Abt.Epdt.WecomSite/node_modules/.pnpm/rollup-plugin-copy@3.5.0/node_modules/rollup-plugin-copy/dist/index.commonjs.js";
function copyWASMPlugin() {
  return copy({
    verbose: true,
    hook: "closeBundle",
    targets: [
      {
        src: ["src/utils/barcodewasm/**/*", "!src/utils/barcodewasm/_framework"],
        dest: "dist/assets"
      },
      {
        src: "src/utils/barcodewasm/_framework/**/*",
        dest: "dist/assets"
      }
    ]
  });
}

// vite.config.ts
var vite_config_default = defineConfig({
  base: "./",
  envPrefix: "ONEPDT_",
  // resolve: { alias: { "#components": path.resolve(__dirname, "src/components") } },
  plugins: [
    // mkcert(),
    vue(),
    vueJsx({
      // options are passed on to @vue/babel-plugin-jsx
    }),
    AutoImport({
      resolvers: [VantResolver()]
    }),
    Components({
      resolvers: [VantResolver()]
    }),
    copyWASMPlugin()
  ],
  server: {
    host: "localhost",
    port: 3e3,
    //本机端口
    proxy: {
      "/api": {
        // target:import.meta.env
        target: "https://localhost:5001/",
        //代理目标服务器地址
        secure: false
      },
      "/Saml2": {
        target: "https://localhost:5001/",
        //代理目标服务器地址
        secure: false
      }
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
