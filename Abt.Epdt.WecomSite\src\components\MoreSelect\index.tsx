import { watch } from "fs";
import { json } from "stream/consumers";
import { Tag,Field ,Popup, CheckboxGroup, Cell, Checkbox } from "vant";
import { defineComponent, FunctionalComponent,  ref ,Ref, computed,defineExpose,nextTick} from "vue";


const MoreSelect: FunctionalComponent<{
    basicDiseaseColumns:Ref<any>;
    feildname: string;
    feildvalue:string;
    isclick:boolean;
    checkedvalue:Ref<[]>;
    onReturnData: (index) => void;

}> = ({ basicDiseaseColumns,feildname,feildvalue,isclick,checkedvalue,onReturnData },context) => {
    console.log("checkedvalue",checkedvalue);
    // checkbox绑定的数组
    var checkBox = ref([]);
    if(checkedvalue!=null&&checkedvalue.value!=null){
        checkBox.value = checkedvalue.value
    }
    // 是否展示弹出层
    const isBasicDiseaseShow = ref(false);
    // 提交内容
    const basicDisease = ref('');
    const handleBoxClick = () => {
        basicDisease.value = ''
        isBasicDiseaseShow.value = false;
        onReturnData(doctorList);
        //(checkBox);
    };

    const checkBoxMethods = (checkItem)=>{
        console.log("checkBoxMethods",checkItem)
        console.log("checkBoxMethods.....checkBox.value",checkBox.value)
        let idx = checkBox.value.findIndex((item)=>{
            return checkItem == item
        });
        if(idx >= 0){
            checkBox.value.splice(idx,1)
        }else{
            checkBox.value.push(checkItem)
        }
    }

    const setcheckBox = {
        setboxValue(value) {
            console.log("setboxValue",value);
            checkBox.value = value;
            console.log("setboxValu后",checkBox.value);
        },
      };

    const doctorList = computed(() =>{
        console.log("computed",checkBox.value)
        let tempstr = "";
        checkBox.value.forEach(element => {
            tempstr += `${element}、`;
        });
        feildvalue = tempstr.slice(0,tempstr.length-1); 
        return tempstr.slice(0,tempstr.length-1);  
    });

    (context as any).expose({
        clearvalue: () => {
            feildvalue = "";
            checkBox.value = [];
        },
        setcheckBox,
        checkBoxMethods,
        isBasicDiseaseShow,
        checkBox
    });


  return () => (
    <div>
    <Field
      modelValue={feildvalue} input-align="right" label={feildname} disabled={!isclick}
      is-link readonly onClick={()=>{ if(isclick){ isBasicDiseaseShow.value = true;} }}
    />
 
    <Popup v-model:show={isBasicDiseaseShow.value} teleport="body" round position="bottom" style="height:345px">
                        <div class="py-4 h-60">
                            <div class="flex justify-between">
                            <button type="button" class="van-picker__cancel van-haptics-feedback" onClick={()=>{isBasicDiseaseShow.value = false;}}>取消</button>
                            <button type="button" class="van-picker__confirm van-haptics-feedback" onClick={()=>{handleBoxClick()}}>确认</button>
                            </div>
                            <CheckboxGroup modelValue={checkBox.value}>
                            
                                {/* {
                                    basicDiseaseColumns.value.map((val,index)=>{
                                        return (<Cell  key={index} title={val.text} >
                                            <Checkbox style="display: flex;  align-items: center;  justify-content: end;" name={val} onClick={ (index)=>{ checkBoxMethods(val)} } />
                                        </Cell>)
                                    })
                                } */}
                                {
                                    basicDiseaseColumns.value.map((val,index)=>{
                                        return (<Cell  key={index} title={val} >
                                            <Checkbox style="display: flex;  align-items: center;  justify-content: end;" name={val} onClick={ (index)=>{ checkBoxMethods(val)} } />
                                        </Cell>)
                                    })
                                }
                         
                            </CheckboxGroup>
                        </div>
    </Popup>
    </div>
  );
};
 
export default defineComponent(MoreSelect, {
    props: ['basicDiseaseColumns','feildname','feildvalue','isclick','checkedvalue', 'onReturnData'],
  });