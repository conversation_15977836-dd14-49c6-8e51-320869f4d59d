<script setup lang="ts">
import { ParamsProps, useParamsStore, useUserStore } from './stores';
import { Button, Calendar, Field, Icon, List, Picker, Popup, showDialog, showToast, showFailToast, showConfirmDialog } from 'vant';
import { storeToRefs } from 'pinia';
import { ref, onMounted, provide, watch, nextTick, onBeforeMount, onUnmounted } from 'vue';
import Header from './components/Header';
import GlobalSelect from './components/Select/wrapper';
import { useSelect } from './hooks';
import { useQuery } from '@tanstack/vue-query';
import { getParams } from './service/params';
import { group } from 'radash';
import request from './service/request';
import { useRoute } from 'vue-router';
import router from './router';
import { getLoginUrl } from './utils/auth';
import useLoading from './hooks/useLoading';
import { loadVConsole } from './utils/basic';
import { useClearSession } from './hooks/useClearSession';


const route = useRoute();
const userStore = useUserStore();
useClearSession();
const selectRef = ref();
const paramsStore = useParamsStore();
const { updateParams } = useParamsStore();
const isFirstRouteChange = ref(true);

const userInfoRef = storeToRefs(userStore);
const { updateUser } = useUserStore();
const { userInfo } = storeToRefs(useUserStore());
const isCheckingLogin = ref(false)
useLoading(isCheckingLogin)


//获取用户角色
const LoginAndGetRole = async () => {
  const redirectBaseUrl = location.origin + location.pathname;
  const fullUrl = window.location.href; // 获取完整的 URL
  const basePath = fullUrl.split('?')[0]; // 去掉查询参数部分
  const abbottemail = route.query.abbottemail;
  if (import.meta.env.MODE !== 'production' && abbottemail !== undefined) {
    const url = `${import.meta.env.ONEPDT_PROXY_URL ?? ''
      }/api/auth/login/skip?abbottemail=${abbottemail}&signonurl=${encodeURIComponent(
        basePath
      )}&errorurl=${encodeURIComponent(redirectBaseUrl + '#/error')}`;
    location.href = url;
    return;
  }

  return new Promise((resolve, reject) => {
    isCheckingLogin.value = true
    request({
      method: 'get',
      url: '/api/auth/me',
    })
      .then((res) => {
        if (res.data.role && res.data.role.length > 0) {
          updateUser((currentUsreInfo) => ({
            ...currentUsreInfo,
            name: res.data.username,
            email: res.data.useremail,
            code: res.data.code,
            id: res.data.userid,
            isexitstodolist: res.data.isexitstodolist,
            isLogin: true,
            Rolelist: res.data.role,
          }));
          if (
            !userInfo.value.roleid ||
            userInfo.value.roleid == '' ||
            userInfo.value.roleid == null ||
            userInfo.value.roleid == undefined
          ) {
            updateUser((currentUsreInfo) => ({
              ...currentUsreInfo,
              role: res.data.role[0].rolename,
              phone: res.data.role[0].phone,
              roleid: res.data.role[0].roleid,
              istodo: res.data.role[0].istodo,
              isquery: res.data.role[0].isquery,
              isproxy: res.data.role[0].isproxy,
              proxyemail: res.data.role[0].proxyemail,
              isLogin: true,
            }));
          }
        }
        localStorage.setItem('previousLoginEmail', res.data.useremail);
        resolve(true);
       
      })
      .catch(async (error) => {
        if (error.response.status === 401) {

          showToast('未登录，正在跳转至登录页');
          const previousLoginEmail = localStorage.getItem('previousLoginEmail');
          const url = getLoginUrl(previousLoginEmail);
          location.href = url;
        } else {
          showFailToast({
            message: error.response.data && error.response.data !== '' ? error.response.data : '系统错误',
            duration: 3000,
          });
        }
        console.log(error);
        resolve(false);
      }).finally(()=>{
         isCheckingLogin.value = false
      })
  });
};

const checkPolicy = async () => {
  console.log(import.meta.env);
  const result = await request({
    url: `/api/policy/CheckPolicyByUser?applicationID=${import.meta.env.ONEPDT_POLICY_APP_ID}&type=1&id=${userInfo.value.code
      }`,
  });
  if (result?.data?.code == '200004' || result?.data?.code === '200003') {
    router.replace({ name: 'consent' });
    return;
  }
};


provide('select', useSelect());



const URLWithoutAuth = ['error', 'auth']

const checkDebug = () => {
  const isDebug = route.query.debug;
  if (isDebug === 'true') {
    loadVConsole()
  }

}

watch(
  () => route.name,
  async (newName) => {
    if (isFirstRouteChange.value && newName && !URLWithoutAuth.includes(newName.toString())) {
      //初次登录检查登录态和授权
      //放在onMounted中刷新的时候无法获取route的name，所以放在watch中
      const isLogined = await LoginAndGetRole();
      if (isLogined) {
        checkPolicy();
      }


      isFirstRouteChange.value = false;
    }
    checkDebug();
  }
);

</script>

<template>
  <div class="text-base flex flex-col epdt h-screen max-h-screen text-black bg-mainbg">
    <div id="home-header"></div>
    <router-view v-slot="{ Component }">
      <Transition name="slide-left" mode="out-in">
        <keep-alive>
          <component :is="Component" v-if="$route.meta.keepAlive" :key="($route as any).href" />
        </keep-alive>
      </Transition>
      <component :is="Component" v-if="!$route.meta.keepAlive" :key="$route.name" />
    </router-view>
    <GlobalSelect></GlobalSelect>
  </div>
</template>
