import { Empty } from 'vant';
import { defineComponent, FunctionalComponent } from 'vue';

const BarcodeResultCard: FunctionalComponent<{ name: string; result: string[] }> = (props) => {
  console.log(props);
  return () => (
    <div class="shadow-xl relative bg-white rounded-md  flex gap-[5px] flex-col px-2 py-[5px] ">
      <div class="font-bold text-md">{props.name}</div>
      <div class="text-sm p-4   text-md text-center">
        {props.result && Array.isArray(props.result) ? (
          props.result.length === 0 ? (
            <div>没有识别到序列号</div>
          ) : (
            props.result.map((item) => <div>{item}</div>)
          )
        ) : (
          <div>{props.result === undefined ? '点击识别按钮开始识别' : '没有识别到序列号'}</div>
        )}
      </div>
    </div>
  );
};

export default defineComponent(BarcodeResultCard, { props: ['name', 'result'] });
