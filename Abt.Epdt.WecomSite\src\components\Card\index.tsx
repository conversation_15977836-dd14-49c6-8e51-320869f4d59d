import { Tag } from "vant";
import { defineComponent, FunctionalComponent } from "vue";

const Card: FunctionalComponent<{ item: any; onClick?: (item) => void }> = (
  props
) => {
  const { item, onClick } = props;
  const onHandleClick = (item) => {
    onClick && onClick(item);
  };
  return () => (
    <div
      onClick={() => onHandleClick(item)}
      class=" shadow-lg relative bg-white rounded-lg flex gap-[5px] flex-col px-2 py-[5px] "
    >
      <div class=" absolute right-0">
      </div>
      <div class="card-title text-abbott font-bold text-sm ">{item.onepdt_hospitalname}</div>
      <div class="card-hospital font-bold text-xs"> 手术编号：{item.onepdt_name}</div>
      <div class="text-xs text-gray-400 flex ">
        <div class="card-patientName" style="width: 52%;">患者姓名：{item.onepdt_patient}</div>
        <div class="card-implantDate">植入日期：{item.onepdt_date}</div>
      </div>
      <div class="text-xs text-gray-400 flex ">
        <div class="card-patientName" style="width: 52%;">主机型号：{item.onepdt_product_name}</div>
        {item.onepdt_approver_text == ""?"":(<div class="card-patientName">审批人：{item.onepdt_approver_text}</div>)}
      </div>
      <div class="text-xs text-gray-400 flex ">
        {/* <div class="card-patientName">状态：{item.onepdt_approval_status_label}</div> */}
        {/* <div class="card-implantDate ml-auto">
          审批状态：{item.onepdt_approval_status_label}
        </div> */}
      </div>
      {/* <div class="text-xs text-gray-400 flex ">
        <div class="card-patientName">退回原因：{item.onepdt_comment}</div>
      </div> */}
    </div>
  );
};

export default defineComponent(Card, { props: ["item", "onClick"] });
