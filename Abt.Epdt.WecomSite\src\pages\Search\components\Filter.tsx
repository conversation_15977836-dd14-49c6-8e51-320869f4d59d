// import { showSelect } from "#src/components/Select";
import { useGlobalSelect, useSelect } from "#src/hooks/index.ts";
import request from "#src/service/request.js";
import dayjs from "dayjs";
import { ActionBar, ActionBarButton, Field,Calendar,Popup ,CheckboxGroup,Checkbox,CellGroup, Cell,Button} from "vant";
import { defineComponent, FunctionalComponent, inject, Ref ,onMounted ,ref,watch,onBeforeMount,onUpdated,expose} from "vue";
import { FilterProps } from "..";
import MoreSelect from "#src/components/MoreSelect/index.tsx";
import { storeToRefs } from "pinia";
import { useUserStore } from "#src/stores/user";

//植入状态
var zhirutype = ref([]); 
var zhirutypestr = ref([]);

var moreselectref = ref(null);

const Filter: FunctionalComponent<{
  isShowCalendar: Ref<boolean>;
  implantDateRange: Ref<[string, string]>;
  hospital: Ref<string>;
  status: Ref<string>;
  filterParams: Ref<FilterProps>;
  implantType: Ref<string>;
  PageType:Ref<string>;
}> = (props,context) => {
  // onMounted(() => {
  //   LoadZhiruType();
  // });
  onMounted(async () => {
    await initData();
  });
  
  const { isShowCalendar, filterParams } = props;
    const { userInfo } = storeToRefs(useUserStore());

  const select = useGlobalSelect();
  const initData = async () => {
    try {
      await Promise.all([
        LoadZhiruType()
      ]);
    } catch (error) {
      console.error(error);
    } 
  };

  const LoadZhiruType = async () => {
    //if (!userInfo.value?.isLogin) return;
    request({
      url :"api/epdt/GetOnepdtConfig?tablename=跟台信息&fieldname=植入类型",
      method:"get"
    }).then(res=>{ 
      console.log("植入类型",res.data);
      zhirutype.value = [];
      zhirutype.value = res.data;
      zhirutypestr.value = res.data.map(o=>o.text);
    });
  };

  const resetFilterParams = () => {
    filterParams.value = {
      searchText: "",
      searchText2: "",
      productname:"",
      submittername:"",
      province:"",
      hospital: "",
      status: "",
      implantType: "",
      implantDateRange: undefined,
    };
    if(moreselectref.value){
      moreselectref.value.clearvalue();
    }
  };

  const onConfirmImplantDate = (date:any) => {
    filterParams.value.implantDateRange =[ dayjs(date[0]).format("YYYY/MM/DD"), dayjs(date[1]).format("YYYY/MM/DD")]; 
    console.log(filterParams.value);
    isShowCalendar.value = false;
  };

  // 使用 expose 暴露 resetFilterParams 方法
  // expose({
  //   resetFilterParams,
  // });

  (context as any).expose({
    resetFilterParams
  });
  
  return () => (
    <div class="min-h-0 overflow-hidden   bg-white  ">
      <div class="w-full flex text-abbott font-bold text-right text-sm mt-2">
        {/* <div></div> */}
        {/* <div class="ml-auto mr-4" onClick={resetFilterParams}>
          清除筛选
        </div> */}
        {/* <Button class=""  size="small" type="danger">重置</Button> */}
      </div>
      <Field
        label="搜索框"
        v-model={filterParams.value.searchText}
        inputAlign="right"
        placeholder={props.PageType.value == "待办"?"患者、医院、手术编号":"患者、手术编号、医院、省份"}
      ></Field>
      {props.PageType.value == "查询"?(<Field
        label="产品型号"
        v-model={filterParams.value.productname}
        inputAlign="right"
        placeholder="产品型号"
      ></Field>):""}
      {props.PageType.value == "查询"?(<Field
        label="填报人"
        v-model={filterParams.value.submittername}
        inputAlign="right"
        placeholder="填报人"
      ></Field>):""}
      {/* {props.PageType.value == "查询"?(<Field
        label="省份"
        v-model={filterParams.value.province}
        inputAlign="right"
        placeholder="省份"
      ></Field>):""} */}
      
      {/* {props.PageType.value == "查询"?( <Field
        label="搜索框"
        v-model={filterParams.value.searchText2}
        inputAlign="right"
        placeholder="患者、医院、手术编号、产品、填写人"
      ></Field>):""} */}
      {
        props.PageType.value == "查询"?(<MoreSelect 
        ref={moreselectref}
        feildname="植入类型" 
        isclick={true}
        feildvalue={filterParams.value.implantType}
        basicDiseaseColumns={zhirutypestr}  
        onReturnData={(moredata) => {
            filterParams.value.implantType = moredata.value;
        }}></MoreSelect>):""
      }
      
      
      <Field
        border
        is-link
        label="植入时间"
        input-align="right"
        modelValue={
          filterParams.value.implantDateRange
            ? `${dayjs(filterParams.value.implantDateRange[0]).format(
                "YYYY/MM/DD"
              )} - ${dayjs(filterParams.value.implantDateRange[1]).format(
                "YYYY/MM/DD"
              )}`
            : ""
        }
        readonly
        onClick={() => {
          isShowCalendar.value = true;
        }}
      >
        {{
            button: () =>
              filterParams.value.implantDateRange ? (
                <div
                  style="cursor: pointer; color: #999; padding: 0 10px;"
                  onClick={(e) => {
                    e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                    filterParams.value.implantDateRange = undefined;
                  }}
                >
                  ✖
                </div>
              ) : null,
        }}

      </Field>

      <Calendar
        class="z-[60]"
        allowSameDay={true}
        teleport='#app'
        v-model:show={isShowCalendar.value}
        type="range"
        onConfirm={onConfirmImplantDate}
        switch-mode="year-month"
        minDate={new Date('1995-01-01')}
        maxDate={new Date('9999-01-01')}
      >
      </Calendar>
    </div>
  );
};

export default defineComponent(Filter, {
  props: [
    "isShowCalendar",
    "implantDateRange",
    "hospital",
    "status",
    "implantType",
    "filterParams",
    "PageType",
  ],
});
