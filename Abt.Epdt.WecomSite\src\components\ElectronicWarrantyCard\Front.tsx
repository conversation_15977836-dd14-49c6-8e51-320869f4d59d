import { defineComponent, FunctionalComponent,Ref,ref } from "vue";
import ElectronicWarrantyCardFrame from "./Frame";
import mr from "#/src/assets/mr.png";
import { Watermark,Image } from "vant";

export type FontModel = {
  name?:string;
  gender?:string;
  cardType?: string;
  cardNo?: string;
  hospital?: string;
  hospitalAdd?: string;
  hospitalTel?: string;
  impDate?: string;
  mainMode?: string;
  manufacturer?: string;
  modelNo?: string;
  onepdt_t_operation_implantid?: string;
  physician1?: string;
  physician2?: string;
  scanIntensity?: string;
  selectedMode?:string;
  sn?:string;
  warrantyPeriod?:string;
  accessories?:Ref<AccessoriesModel[]>[];
};
export type AccessoriesModel = {
  sn?:string;
  modelNo?:string;
  impDate?: string;
  manufacturer?: string;
};


const ElectronicwarrentyCardFront: FunctionalComponent<{
  FontData: FontModel;
  Status: Ref<string>;
  id: string;
}> = (props) => {
  return () => (
    <div id={`${props.id}-front`}>
      {/* <span class="font-bold">识别卡类型:{props.FontData.cardType}</span>  */}
      {/* <span class="font-bold">{props.Status}</span>  */}
      <ElectronicWarrantyCardFrame FrameData={props.FontData}>        
        <div class="flex relative z-10">
          <div class="flex w-full pt-[2%]">
            <div class="flex-1">
              <div class="flex ">
                <span>姓名 Name:</span>
                <span class="text-[rgb(1_156_222)]">{props.FontData.name}</span>
              </div>
              <div class="flex ">
                <span>性别 Gender:</span>
                <span class="text-[rgb(1_156_222)]">{props.FontData.gender}</span>
              </div>
              <div class="flex ">
                <span>植入日期 Imp. Date:</span>
                <span class="text-[rgb(1_156_222)]">{props.FontData.impDate}</span>
              </div>             
            </div>

            <div class=" flex-1">
              <div class="flex ">
                <span>产品型号 Model No.:</span>
                <span class="text-[rgb(1_156_222)]">{props.FontData.modelNo}</span>
              </div>
              <div class="flex ">
                <span>序列号 Serial No.:</span>
                <span class="text-[rgb(1_156_222)]">{props.FontData.sn}</span>
              </div>
              <div class="flex ">
                <span>担保年限 Warranty Period:</span>
                <span class="text-[rgb(1_156_222)]">{props.FontData.warrantyPeriod}</span>
              </div>
              <div class="flex ">
                <span>制造商品牌 Manufacturer:</span>
                <span class="text-[rgb(1_156_222)]">{props.FontData.manufacturer}</span>
              </div>
              {(props.FontData.cardType == "B") ? (<div class="flex ">
                <span>扫描强度 MRI Strength:</span>
                <span class="text-[rgb(1_156_222)]">{props.FontData.scanIntensity}</span>
              </div>):(<div class="flex " style="visibility: hidden">
                <span>扫描强度 MRI Strength:</span>
                <span class="text-[rgb(1_156_222)]"></span>
              </div>)}
            </div>
          </div>
        </div>
        <div style="position: relative;z-index: 999;">
          <div class="flex ">
            <span>植入医生 Physician:</span>
            <span class="text-[rgb(1_156_222)] ">{props.FontData.physician1}</span>
          </div>
          <div class="flex ">
            <span>植入医院 Hospital:</span>
            <span class="text-[rgb(1_156_222)] ">{props.FontData.hospital}</span>
          </div>
          <div class="flex ">
            <span>医院地址 Hospital Add.:</span>
            <span class="text-[rgb(1_156_222)] ">{props.FontData.hospitalAdd}</span>
          </div>
          <div class="flex">
            <span>医院电话 Hospital Tel.:</span>
            <span class="text-[rgb(1_156_222)] ">{props.FontData.hospitalTel}</span>
          </div>
        </div>

        <div class="h-[20%] pt-[0%] flex items-center">
          <div style="width: 100%;">
          <p style="position: relative;z-index: 999;">如您有核磁共振 (MRI) 检查需求，请咨询当地医院或医生。</p>
          <p style="position: relative;z-index: 999;">
            For MRI scanning needs, please consult local hospital or doctor.
          </p>
          </div>
          {(props.FontData.cardType == "B" || props.FontData.cardType == "C") ? (<Image class="w-[19%]" style="margin-left: auto;margin-bottom: 14px;" src={mr}></Image>):""}           
        </div>        
      </ElectronicWarrantyCardFrame>
    </div>
  );
};

export default defineComponent(ElectronicwarrentyCardFront, {
  props: ["FontData","Status","id"],
});
