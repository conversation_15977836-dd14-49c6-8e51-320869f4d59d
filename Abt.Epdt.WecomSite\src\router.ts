import { createRouter, createWebHashHistory } from 'vue-router';

import Home from './pages/Home';
import Edit from './pages/Edit';
import Scan from './pages/Scan';
import PreviewCard from './pages/PreviewCard';
import SubmitResult from './pages/SubmitResult';
import Todo from './pages/Todo';
import Error from './pages/Error';
import Consent from './pages/Consent';
import LandingPage from './pages/LandingPage';
import Auth from './pages/Auth';

const routes = [
  { path: '/', name: 'home', meta: { keepAlive: true }, component: Home },
  { path: '/edit', name: 'edit', meta: { keepAlive: true }, component: Edit },
  { path: '/filepreview', name: 'filepreview', meta: { keepAlive: true }, component: () => import(/* webpackChunkName: "filepreview" */ './pages/FilePreview') },
  { path: '/todo', name: 'todo', meta: { keepAlive: false }, component: Todo },
  { path: '/scan', name: 'scan', meta: { keepAlive: false }, component: Scan },
  { path: '/consent', name: 'consent', meta: { KeepAlive: false }, component: Consent },
  { path: '/error', name: 'error', meta: { KeepAlive: false }, component: Error },
  { path: '/landingpage', name: 'landingpage', meta: { KeepAlive: false }, component: LandingPage },
  {
    path: '/previewcard',
    name: 'previewcard',
    meta: { keepAlive: false },
    component: PreviewCard,
  },
  {
    path: '/submit',
    name: 'submit',
    meta: { keepAlive: false },
    component: SubmitResult,
  },
  {
    path: '/barcodescan',
    name: 'barcodescan',
    meta: { keepAlive: false },
    component: ()=>import(/* webpackChunkName: "detail" */ './pages/BarcodeScan'),
  },
  {
    path:'/auth',
    name:'auth',
    meta: { keepAlive: false },
    component: Auth
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
  routes,
});

export default router;
