import { <PERSON><PERSON>, <PERSON>p<PERSON><PERSON> } from 'jimp';
import * as main from './barcodewasm/main';
import cv, { blur } from '@techstark/opencv-js';
// import { BarcodeFormat, DecodeHintType } from '@zxing/library';
// import { BrowserMultiFormatReader } from '@zxing/browser';

const calculateMaxResolution = (width, height, aspectRatio) => {
  //   const aspectRatio = 3 / 4; // 3:4比例

  // 判断最大边是宽还是高
  if (width > height) {
    const maxWidth = width;
    const maxHeight = Math.round(maxWidth / aspectRatio);

    if (maxHeight > height) {
      const maxHeight = height;
      const maxWidth = Math.round(maxHeight * aspectRatio);
      return { width: maxWidth, height: maxHeight };
    }
    return { width: maxWidth, height: maxHeight };
  } else {
    const maxHeight = height;
    const maxWidth = Math.round(maxHeight * aspectRatio);

    if (maxWidth > width) {
      const maxWidth = width;
      const maxHeight = Math.round(maxWidth / aspectRatio);
      return { width: maxWidth, height: maxHeight };
    }
    return { width: maxWidth, height: maxHeight };
  }
};

const extractSerialNumber = (barcode) => {
  let index = 0;
  const length = barcode.length;
  const FNC1 = String.fromCharCode(29); // FNC1 分隔符

  // 检查并提取  "01" 和14位的 GTIN 码
  if (barcode.substring(index, index + 2) === '01') {
    index += 2;
    const gtin = barcode.substring(index, index + 14);
    index += 14;
  } else {
    return null;
  }

  // 检查是否存在  "17" 和6位的效期
  if (barcode.substring(index, index + 2) === '17') {
    index += 2;
    const expiry = barcode.substring(index, index + 6);
    index += 6;
  }

  // 检查并提取  "21" 和序列号
  if (barcode.substring(index, index + 2) === '21') {
    index += 2;
    let endIndex = barcode.indexOf(FNC1, index);
    if (endIndex === -1) {
      endIndex = length;
    }
    const serialNumber = barcode.substring(index, endIndex);
    return serialNumber;
  } else {
    return null;
  }
};

export function prehandleImageByOpenCV(base64Image) {
  return new Promise((resolve, reject) => {
    // 等待 OpenCV.js 加载完成
    if (typeof cv === 'undefined') {
      reject(new Error('OpenCV.js 未加载'));
      return;
    }
    const img = new Image();
    img.src = base64Image;

    img.onload = () => {
      // 创建一个隐藏的 canvas 元素
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0, img.width, img.height);

      try {
        let src1 = cv.imread(canvas);
        let gray1 = new cv.Mat();
        //转灰度
        cv.cvtColor(src1, gray1, cv.COLOR_RGBA2GRAY, 0);

        // // cv.imshow(document.getElementById('step0'), gray1);
        // //对比度增强
        // // cv.equalizeHist(gray1, gray1);
        // // cv.imshow(document.getElementById('step1'), gray1);
        // //去噪/平滑
        // cv.GaussianBlur(gray1, gray1, new cv.Size(1, 1), 0);
        // // cv.imshow(document.getElementById('step2'), gray1);
        // //二值化
        // // cv.threshold(gray1, gray1, 128, 255, cv.THRESH_BINARY);
        // // cv.imshow(document.getElementById('step3'), gray1);
        // let kernel = cv.getStructuringElement(cv.MORPH_RECT, new cv.Size(3, 3));
        // //形态学操作（闭运算）
        // // cv.morphologyEx(gray1, gray1, cv.MORPH_CLOSE, kernel);
        // // cv.imshow(document.getElementById('step3'), gray1);
        // // resolve(gray1);

        cv.imshow(document.getElementById('step1'), gray1);

        //  cv.blur(gray1, gray1, new cv.Size(9, 9));

        // cv.threshold(gray1, gray1, 10, 255, cv.THRESH_BINARY+cv.THRESH_OTSU);
        // cv.threshold(gray1, gray1, 120, 255, cv.THRESH_BINARY);

        // let kernel2 = cv.Mat.ones(1, 10, cv.CV_8U);
        // cv.morphologyEx(gray1, gray1, cv.MORPH_BLACKHAT, kernel2);
        //  cv.equalizeHist(gray1,gray1)

        cv.imshow(document.getElementById('step2'), gray1);
        // cv.imshow(document.getElementById('step1'), gray1);
        // let kernel4 = cv.getStructuringElement(cv.MORPH_RECT, new cv.Size(21, 7));
        // cv.morphologyEx(gray1, gray1, cv.MORPH_CLOSE, kernel4);
        // cv.threshold(gray1, gray1, 30, 255, cv.THRESH_BINARY);
        // cv.imshow(document.getElementById('step3'), gray1);
        // let inverted = new cv.Mat();
        // cv.bitwise_not(gray1, gray1);

        cv.imshow(document.getElementById('step4'), gray1);
        let canvasEle = document.getElementById('step2') as HTMLCanvasElement;
        // let dilateKernel = cv.Mat.ones(1.5, 1.5, cv.CV_8U);
        // cv.morphologyEx(
        //   gray1,
        //   gray1,
        //   cv.MORPH_DILATE,
        //   dilateKernel,
        //   new cv.Point(-1, -1),
        //   1,
        //   cv.BORDER_CONSTANT,
        //   cv.morphologyDefaultBorderValue()
        // );

        // // 将 Canvas 转换为 Base64
        let base64String = canvasEle.toDataURL('image/png', 1);
        // cv.imshow(document.getElementById('step3'), gray1);
        // debugger;
        resolve(base64String);
      } catch (err) {
        console.error(err);
        reject(err);
      }
    };
  });
}

/**
 * 检测图片中的条形码位置
 * @param {string} base64Image - Base64 编码的图片 URL
 * @returns {Promise<Array<{x: number, y: number, width: number, height: number}>>} - 返回条形码的位置数组
 */
export function detectBarcodes(base64Image) {
  return new Promise((resolve, reject) => {
    // 等待 OpenCV.js 加载完成
    if (typeof cv === 'undefined') {
      reject(new Error('OpenCV.js 未加载'));
      return;
    }

    // 创建一个 Image 对象
    const img = new Image();
    img.src = base64Image;

    img.onload = () => {
      // 创建一个隐藏的 canvas 元素
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0, img.width, img.height);

      try {
        console.log('start convert');
        //start your code here
        // 使用 OpenCV.js 读取图像
        let src = cv.imread(canvas);
        // let gray1 = new cv.Mat();
        cv.cvtColor(src, src, cv.COLOR_RGBA2GRAY, 0);

        const scale = 800 / src.cols;
        const newWidth = 800;
        const newHeight = Math.round(src.rows * scale);
        // let resizedGray = new cv.Mat();
        cv.resize(src, src, new cv.Size(newWidth, newHeight), 0, 0, cv.INTER_AREA);
        // let kernel2 = cv.getStructuringElement(cv.MORPH_RECT, new cv.Size(21, 7));

        let kernel2 = cv.Mat.ones(1, 10, cv.CV_8U);
        cv.morphologyEx(src, src, cv.MORPH_BLACKHAT, kernel2);

        // cv.imshow(document.getElementById('step1'), gray1);
        // let kernel4 = cv.getStructuringElement(cv.MORPH_RECT, new cv.Size(21, 7));
        // cv.morphologyEx(gray1, gray1, cv.MORPH_CLOSE, kernel4);
        cv.threshold(src, src, 30, 255, cv.THRESH_BINARY);

        // cv.imshow(document.getElementById('step2'), gray1);
        let dilateKernel = cv.Mat.ones(1, 5, cv.CV_8U);
        cv.morphologyEx(src, src, cv.MORPH_DILATE, dilateKernel, new cv.Point(2, 0), 2);
        // cv.imshow(document.getElementById('step3'), gray1);
        // let closeKernel = cv.Mat.ones(1, 5, cv.CV_8U);
        cv.morphologyEx(src, src, cv.MORPH_CLOSE, dilateKernel, new cv.Point(2, 0), 2);
        // cv.imshow(document.getElementById('step4'), gray1);

        let openKernel = cv.Mat.ones(21, 35, cv.CV_8U);
        cv.morphologyEx(src, src, cv.MORPH_OPEN, openKernel);
        // cv.imshow(document.getElementById('step5'), gray1);

        // let kernel3 = cv.getStructuringElement(cv.MORPH_RECT, new cv.Size(21, 7));

        //     const kernel2 = cv.Mat.ones(1, 5, cv.CV_8U)

        let contours = new cv.MatVector();
        let hierarchy = new cv.Mat();
        cv.findContours(src, contours, hierarchy, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE);

        // console.log(contours.size());
        // 8. 筛选轮廓
        const barcodeLocations = [];
        let unscale = 1.0 / scale;
        for (let i = 0; i < contours.size(); ++i) {
          let contour = contours.get(i);
          let rect = cv.boundingRect(contour);
          let x = rect.x;
          let y = rect.y;
          let width = rect.width;
          let height = rect.height;

          const area = width * height;
          const aspectRatio = width / height;

          // 面积和长宽比阈值，这里可以根据你的实际情况调整
          // if (area > 1000 && aspectRatio > 2 && aspectRatio < 10) {
          if (aspectRatio > 4) {
            let rotatedRect = cv.minAreaRect(contour);
            // 获取旋转角度（注意：角度单位是度，范围通常是 -90 到 0）
            let angle = rotatedRect.angle;
            barcodeLocations.push({
              x: x * unscale,
              y: y * unscale,
              width: width * unscale,
              height: height * unscale,
              angle,
            });
          }

          // contour.delete();
          // rect.delete();
        }
        src.delete();
        // gray1.delete();

        resolve(barcodeLocations.sort((a, b) => a.y - b.y));

        // 使用 Sobel 算子进行边缘检测
        // let gradX = new cv.Mat();
        // let gradY = new cv.Mat();
        // cv.Sobel(gray, gradX, cv.CV_32F, 1, 0, -1);
        // cv.Sobel(gray, gradY, cv.CV_32F, 0, 1, -1);

        // let gradient = new cv.Mat();
        // cv.subtract(gradX, gradY, gradient);
        // cv.convertScaleAbs(gradient, gradient);

        // // 模糊和阈值处理
        // cv.blur(gradient, gradient, new cv.Size(9, 9));
        // cv.threshold(gradient, gradient, 225, 255, cv.THRESH_BINARY);

        // // 使用闭运算连接可能的条形码区域
        // let kernel = cv.getStructuringElement(cv.MORPH_RECT, new cv.Size(21, 7));
        // cv.morphologyEx(gradient, gradient, cv.MORPH_CLOSE, kernel);

        // // 查找轮廓
        // let contours = new cv.MatVector();
        // let hierarchy = new cv.Mat();
        // cv.findContours(gradient, contours, hierarchy, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE);

        // // 存储条形码位置的数组
        // let barcodePositions = [];

        // // 遍历所有轮廓，筛选可能的条形码
        // for (let i = 0; i < contours.size(); i++) {
        //   let cnt = contours.get(i);
        //   let rect = cv.boundingRect(cnt);
        //   let aspectRatio = rect.width / rect.height;

        //   // 假设条形码的长宽比大于一定值
        //   if (aspectRatio > 2 && rect.width > 400 && rect.height > 20) {
        //     barcodePositions.push({
        //       x: rect.x,
        //       y: rect.y,
        //       width: rect.width,
        //       height: rect.height,
        //     });
        //   }
        //   cnt.delete();
        // }

        // // 清理资源
        // src.delete();
        // gray.delete();
        // gradX.delete();
        // gradY.delete();
        // gradient.delete();
        // kernel.delete();
        // contours.delete();
        // hierarchy.delete();

        // resolve(barcodePositions);
        // return;

        // const src = cv.imread(canvas);

        // // 2. 灰度化
        // let gray = new cv.Mat();
        // cv.cvtColor(src, gray, cv.COLOR_RGBA2GRAY);
        // let kernel1 = cv.getStructuringElement(cv.MORPH_RECT, new cv.Size(21, 7));
        // cv.morphologyEx(gray, gray, cv.MORPH_BLACKHAT, cv.Mat.ones(1, 3, cv.CV_8U));
        // cv.imshow(document.getElementById('opencv'), gray);
        // // 3. Sobel 边缘检测
        // let gradX = new cv.Mat();
        // let gradY = new cv.Mat();
        // cv.Sobel(gray, gradX, cv.CV_32F, 1, 0, 3);
        // cv.Sobel(gray, gradY, cv.CV_32F, 0, 1, 3);

        // // cv.imshow(document.getElementById('opencv'), gradY);
        // // 4. 计算梯度幅值和方向
        // let absGradX = new cv.Mat();
        // let absGradY = new cv.Mat();
        // cv.convertScaleAbs(gradX, absGradX);
        // cv.convertScaleAbs(gradY, absGradY);

        // let grad = new cv.Mat();
        // cv.addWeighted(absGradX, 0.5, absGradY, 0.5, 0, grad);

        // // cv.imshow(document.getElementById('opencv'), grad);

        // // 5. 梯度幅值阈值化
        // let thresh = new cv.Mat();
        // cv.threshold(grad, thresh, 80, 255, cv.THRESH_BINARY);

        // // cv.imshow(document.getElementById('opencv'), thresh);
        // // 6. 形态学闭操作
        // // let kernel = cv.Mat.ones(5, 15, cv.CV_8U);
        // let kernel = cv.Mat.ones(21, 35, cv.CV_8U);
        // let closed = new cv.Mat();
        // cv.morphologyEx(thresh, closed, cv.MORPH_CLOSE, kernel);

        // cv.imshow(document.getElementById('opencv'), closed);
        // // 7. 寻找轮廓
        // let contours = new cv.MatVector();
        // let hierarchy = new cv.Mat();
        // cv.findContours(closed, contours, hierarchy, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE);

        // console.log(contours.size());
        // // 8. 筛选轮廓
        // const barcodeLocations = [];
        // for (let i = 0; i < contours.size(); ++i) {
        //   let contour = contours.get(i);
        //   let rect = cv.boundingRect(contour);
        //   let x = rect.x;
        //   let y = rect.y;
        //   let width = rect.width;
        //   let height = rect.height;

        //   const area = width * height;
        //   const aspectRatio = width / height;

        //   // 面积和长宽比阈值，这里可以根据你的实际情况调整
        //   // if (area > 1000 && aspectRatio > 2 && aspectRatio < 10) {
        //   if (true) {
        //     barcodeLocations.push({ x, y, width, height });
        //   }

        //   // contour.delete();
        //   // rect.delete();
        // }

        // // 释放资源
        // src.delete();
        // gray.delete();
        // gradX.delete();
        // gradY.delete();
        // absGradX.delete();
        // absGradY.delete();
        // grad.delete();
        // thresh.delete();
        // kernel.delete();
        // closed.delete();
        // contours.delete();
        // hierarchy.delete();

        // resolve(barcodeLocations);
      } catch (err) {
        console.error(err);
        reject(err);
      }
    };

    img.onerror = (err) => {
      reject(new Error('无法加载图片'));
    };
  });
}

export function drawBarcodesOnImage(base64Image, barcodes) {
  const img = new Image();
  img.src = base64Image;

  img.onload = () => {
    // 创建一个 canvas 元素用于显示结果
    const canvas = document.getElementById('draw') as HTMLCanvasElement;
    canvas.width = img.width;
    canvas.height = img.height;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(img, 0, 0, img.width, img.height);

    // 绘制红色矩形框
    ctx.strokeStyle = 'red';
    ctx.lineWidth = 3;
    barcodes.forEach((barcode) => {
      // ctx.strokeRect(barcode.x, barcode.y, barcode.width, barcode.height);
      ctx.strokeRect(barcode.x - 10, barcode.y - 10, barcode.width + 20, barcode.height + 20);
    });

    // 将 canvas 添加到页面中（例如 body）
    // document.body.appendChild(canvas);
  };

  img.onerror = (err) => {
    console.error('无法加载图片用于绘制:', err);
  };
}

export const decodeBarcodes = async (base64ImageArr: string[]) => {
  // debugger;
  const base64PromiseAll = base64ImageArr.map((base64Image) => decodeBarcode(base64Image));
  const results = await Promise.all(base64PromiseAll);
  return results;
};

export const decodeBarcode = async (url) => {
  // try {
  //   const data = await utils.wasm.decodeImage(url.split(',')[1]);
  //   const dataArr = JSON.parse(data);
  //   // console.log(dataArr);
  //   // 创建 BrowserBarcodeReader 实例
  //   const hints = new Map();
  //   hints.set(DecodeHintType.POSSIBLE_FORMATS, [
  //     BarcodeFormat.CODE_128,
  //     // 添加其他你需要支持的条形码格式
  //   ]);
  //   hints.set(DecodeHintType.TRY_HARDER, true);
  //   // hints.set(DecodeHintType.CHARACTER_SET, 'UTF-8');
  //   hints.set(DecodeHintType.PURE_BARCODE, true);
  //   const codeReader = new BrowserMultiFormatReader(hints);
  //   // 将 base64 转换为 Image 对象
  //   const img = new Image();
  //   img.src = url; // 你的base64字符串
  //   // 等待图片加载完成
  //   await new Promise((resolve) => {
  //     img.onload = resolve;
  //   });
  //   // 创建 canvas
  //   const canvas = document.createElement('canvas');
  //   canvas.width = img.width;
  //   canvas.height = img.height;
  //   // 将图片绘制到 canvas
  //   const ctx = canvas.getContext('2d');
  //   ctx.drawImage(img, 0, 0);
  //   try {
  //     const result = await codeReader.decodeFromCanvas(canvas);
  //     return result.getText();
  //   } catch (error) {
  //     return '';
  //   }
  //   // 解码条形码
  //   // const result = await codeReader.decodeFromImageUrl(url);
  //   // 返回解码结果
  // } catch (error) {
  //   console.error('解码失败:', error);
  //   throw error;
  // }
};

export async function calculateImageBrightness(base64Image) {
  try {
    // 等待 OpenCV.js 加载完成
    // await cv.ready;

    // 将 base64 转换为图片数据
    const image = (await loadImageFromBase64(base64Image)) as HTMLImageElement;

    // 创建 Mat 对象
    const src = cv.imread(image);

    // 转换为 HSV 颜色空间
    const hsv = new cv.Mat();
    cv.cvtColor(src, hsv, cv.COLOR_BGR2HSV);

    // 分离通道，我们只需要 V 通道(亮度)
    const channels = new cv.MatVector();
    cv.split(hsv, channels);
    const vChannel = channels.get(2); // V 通道

    // 计算平均亮度
    const mean = cv.mean(vChannel);
    const brightness = mean[0]; // 取第一个值，范围 0-255

    // 释放内存
    src.delete();
    hsv.delete();
    channels.delete();
    vChannel.delete();

    return brightness / 255;

    // 返回亮度值和归一化的亮度值(0-1之间)
    // return {
    //   brightness: brightness,
    //   normalizedBrightness: brightness / 255
    // };
  } catch (error) {
    console.error('计算图片亮度出错:', error);
    throw error;
  }
}

// 辅助函数：将 base64 转换为图片
function loadImageFromBase64(base64) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = base64;
  });
}

export function cropBarcodesFromImage(base64Image, barcodePositions) {
  const offset = 20;
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = base64Image;

    img.onload = async () => {
      try {
        // const croppedBase64Images = [];

        const croppedBase64Images = await Promise.all(
          barcodePositions.map(async (pos, index) => {
            // 创建一个 canvas 元素用于裁剪
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // 设置 canvas 尺寸为条形码区域的大小
            // canvas.width = img.width;
            // canvas.height = img.height;
            // canvas.width = pos.width + offset * 2;
            // canvas.height = pos.height + offset * 2;

            // 计算裁剪区域的坐标和尺寸，确保不超出图片边界
            const sourceX = Math.max(0, pos.x - offset);
            const sourceY = Math.max(0, pos.y - offset);
            const sourceWidth = Math.min(pos.width + offset * 2, img.width - sourceX);
            const sourceHeight = Math.min(pos.height + offset * 2, img.height - sourceY);

            canvas.width = sourceWidth;
            canvas.height = sourceHeight;
            // 计算目标canvas的尺寸
            // 保持原始条形码的宽高比
            // canvas.width = pos.width;
            // canvas.height = pos.height;

            // 计算实际的偏移量（考虑边界情况）
            const actualOffsetX = pos.x - sourceX;
            const actualOffsetY = pos.y - sourceY;

            ctx.drawImage(
              img,
              sourceX,
              sourceY,
              sourceWidth,
              sourceHeight,
              0,
              0,
              sourceWidth,
              sourceHeight
            );
            // 获取裁剪后的图片的 Base64 编码
            await new Promise((res) => setTimeout(res, 0));
            const croppedBase64 = canvas.toDataURL('image/png', 1);
            return { content: croppedBase64, angle: pos.angle };
          })
        );

        resolve(croppedBase64Images);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('无法加载图片'));
    };
  });
}
export const preHandleImage = async (barcodeImgArr, plan) => {
  const preHandleArr = barcodeImgArr.map(async (barcodeImg) => {
    type JIMPInstance = Awaited<ReturnType<typeof Jimp.read>>;
    let img = (await Jimp.read(barcodeImg.content)) as unknown as JIMPInstance;

    if (Math.abs(barcodeImg.angle) > 3.5 && barcodeImg.angle < 86.5) {
      img = (await img.rotate(barcodeImg.angle)) as unknown as JIMPInstance;
    }
    let preHandleImageBase64Url = '';

    if (plan === 1) {
      preHandleImageBase64Url = await img
        .greyscale() //去除颜色
        .normalize() //
        .contrast(0.5)
        .threshold({ max: 128 })
        .posterize(2)
        .getBase64(JimpMime.png);
    } else {
      preHandleImageBase64Url = await img
        .greyscale() //去除颜色
        .normalize() //
        .brightness(1.5)
        .contrast(0.5)
        .threshold({ max: 128 })
        .posterize(2)
        .getBase64(JimpMime.png);
    }

    // let img = (await Jimp.read(url))

    // preHandleImageBase64Url = await img

    // .greyscale() //去除颜色
    // .contrast(0.5)
    // .gaussian(1)
    // .blur(1)
    // .threshold({ max: 150 })
    // .resize({w:800,h:800})
    // .posterize(2)
    // .scaleToFit({w:800,h:800})
    // .normalize() //
    // // .blur(1)
    // // .gaussian(1)
    // // .brightness(1.5)
    // .contrast(0.5)
    // .threshold({ max: 128 })
    // .posterize(2)
    // .getBase64(JimpMime.png);
    // const kernel = [
    //     [ 0, -1,  0],
    //     [-1,  5, -1],
    //     [ 0, -1,  0]
    //   ];

    //final version
    // if (plan === 1) {
    //   preHandleImageBase64Url = await img
    //     // .scaleToFit({w:1000,h:1000})
    //     .greyscale() //去除颜色
    //     .normalize() //
    //     .contrast(0.5)
    //     .threshold({ max: 128 })
    //     .posterize(2)
    //     .getBase64(JimpMime.png);
    // } else {
    //   preHandleImageBase64Url = await img
    //     // .scaleToFit({w:1000,h:1000})
    //     .greyscale() //去除颜色
    //     .normalize() //
    //     .brightness(1.5)
    //     .contrast(0.5)
    //     // .brightness(1.5)
    //     .threshold({ max: 128 })
    //     .posterize(2)
    //     .getBase64(JimpMime.png);
    // }

    return preHandleImageBase64Url;
  });
  const preHandleResult = await Promise.all(preHandleArr);
  return preHandleResult;
};

// export const calculateBrightness = async (base64: string) => {
//   let totalBrightness = 0;
//   let pixelCount = 0;

//   const img = await Jimp.read(base64);
//   img.greyscale().scan(0, 0, img.bitmap.width, img.bitmap.height, function (_, __, idx) {
//     const pixelBrightness = this.bitmap.data[idx] / 255; // 因为是灰度图，R=G=B
//     totalBrightness += pixelBrightness;
//     pixelCount++;
//   });
//   const averageBrightness = totalBrightness / pixelCount;
//   return averageBrightness;
//   // console.log('average brightness:', averageBrightness);
// };

export const preHandleAndDecodeImage = (barcodeImgArr: { content: string; angle: number }[]) => {
  return barcodeImgArr.map(async (barcodeImg) => {
    // console.log("brightnessRes",brightnessRes);
    const barcodeImagePostHandle = await preHandleImage([barcodeImg], 1);
    let res = await utils.decodeSingleImage(barcodeImagePostHandle[0].split(',')[1]);
    if (res === '') {
      console.log('try plan 2');
      const barcodeImagePostHandle = await preHandleImage([barcodeImg], 2);
      res = await utils.decodeSingleImage(barcodeImagePostHandle[0].split(',')[1]);
    }
    return res;
  });
};

const wasm = main;
export const decodeSingleImage = (url) => {
  return wasm.decodeSingleImage(url);
};

const utils = {
  wasm: main,
  calculateMaxResolution: calculateMaxResolution,
  extractSerialNumber: extractSerialNumber,
  detectBarcodes: detectBarcodes,
  drawBarcodesOnImage: drawBarcodesOnImage,
  cropBarcodesFromImage: cropBarcodesFromImage,
  decodeBarcode: decodeBarcode,
  preHandleImage: preHandleImage,
  decodeSingleImage: decodeSingleImage,
  calculateImageBrightness: calculateImageBrightness,
  // name:'util'
};

export default utils;
