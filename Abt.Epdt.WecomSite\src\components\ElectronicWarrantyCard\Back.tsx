import { defineComponent, FunctionalComponent,Ref } from "vue";
import ElectronicWarrantyCardFrame from "./Frame";
import {FontModel} from "./Front"

const ElectronicwarrentyCardBack: FunctionalComponent<{
  BackData: FontModel;
  Status: Ref<string>;
}> = (props) => {
  return () => (
    <div>
      {/* <span class="font-bold">识别卡类型:{props.BackData.cardType}</span>  */}
      {/* <span class="font-bold">{props.Status}</span>  */}
      <ElectronicWarrantyCardFrame FrameData={props.BackData}>
        <div class="z-10 relative w-full h-[85%] text-card ">
          <div class="h-[80%] box-border  w-full">
            <div class="flex relative z-10">
              <div class="flex w-full pt-[2%]">
                <div class="flex-1">
                  <div class="flex">
                    <span>产品型号 Model No.:</span>
                    <span class="text-[rgb(1_156_222)]">{props.BackData.modelNo}</span>
                  </div>
                  <div class="flex">
                    <span>序列号 Serial No.:</span>
                    <span class="text-[rgb(1_156_222)]">{props.BackData.sn}</span>
                  </div>
                  <div class="flex " style="visibility: hidden">
                    <span>序列号 Serial No.:</span>
                    <span class="text-[rgb(1_156_222)]">{props.BackData.sn}</span>
                  </div>
                </div>

                <div class=" flex-1">
                  <div class="flex ">
                    <span>所选模式 Selected Mode:</span>
                    <span class="text-[rgb(1_156_222)]">{props.BackData.selectedMode}</span>
                  </div>
                  <div class="flex">
                    <span>主要模式 Main Mode:</span>
                    <span class="text-[rgb(1_156_222)]">{props.BackData.mainMode}</span>
                  </div>
                  <div class="flex " style="visibility: hidden">
                    <span>序列号 Serial No.:</span>
                    <span class="text-[rgb(1_156_222)]"></span>
                  </div>
                </div>
              </div>
            </div>

            <div class="pt-[1%]">
              <div class="flex gap-[5%]">
                <p class="w-[10ch]"></p>
                <p class="w-[11ch]">
                  <span class="">
                    <div>产品型号</div>
                    <div style="font-size: smaller;"> Model No.</div>
                  </span>{" "}
                </p>
                <p class="w-[11ch]">
                  <span class="">
                    <div>序列号</div>
                    <div style="font-size: smaller;">Serial No.</div>
                  </span>{" "}
                </p>
                <p class="w-[13ch]">
                  <span class="">
                    <div>植入日期</div>
                    <div style="font-size: smaller;">Imp. Date</div>
                  </span>{" "}
                </p>
                <p class="w-[11ch]">
                  <span class="">
                    <div>制造商品牌</div>
                    <div style="font-size: smaller;">Manufacturer</div>
                  </span>{" "}
                </p>
              </div>
              <div class="flex gap-[5%]">
                  <p class="w-[11ch] " style="font-size: smaller;">电极Lead1:</p>
                  {props.BackData.accessories.length > 0 ? (<p class="w-[11ch]"><span class="text-[rgb(1_156_222)]"><div>{props.BackData.accessories[0].modelNo}</div></span></p>):""}
                  {props.BackData.accessories.length > 0 ? (<p class="w-[11ch]"><span class="text-[rgb(1_156_222)]"><div>{props.BackData.accessories[0].sn}</div></span></p>):""}
                  {props.BackData.accessories.length > 0 ? (<p class="w-[13ch]"><span class="text-[rgb(1_156_222)]"><div>{props.BackData.accessories[0].impDate}</div></span></p>):""}
                  {props.BackData.accessories.length > 0 ? (<p class="w-[11ch]"><span class="text-[rgb(1_156_222)]"><div>{props.BackData.accessories[0].manufacturer}</div></span></p>):""}
              </div>
              <div class="flex gap-[5%]">
                  <p class="w-[11ch] " style="font-size: smaller;">电极Lead2:</p>
                  {props.BackData.accessories.length > 1 ? (<p class="w-[11ch]"><span class="text-[rgb(1_156_222)]"><div>{props.BackData.accessories[1].modelNo}</div></span></p>):""}
                  {props.BackData.accessories.length > 1 ? (<p class="w-[11ch]"><span class="text-[rgb(1_156_222)]"><div>{props.BackData.accessories[1].sn}</div></span></p>):""}
                  {props.BackData.accessories.length > 1 ? (<p class="w-[13ch]"><span class="text-[rgb(1_156_222)]"><div>{props.BackData.accessories[1].impDate}</div></span></p>):""}
                  {props.BackData.accessories.length > 1 ? (<p class="w-[11ch]"><span class="text-[rgb(1_156_222)]"><div>{props.BackData.accessories[1].manufacturer}</div></span></p>):""}
              </div>
              <div class="flex gap-[5%]">
                  <p class="w-[11ch] " style="font-size: smaller;">电极Lead3:</p>
                  {props.BackData.accessories.length > 2 ? (<p class="w-[11ch]"><span class="text-[rgb(1_156_222)]"><div>{props.BackData.accessories[2].modelNo}</div></span></p>):""}
                  {props.BackData.accessories.length > 2 ? (<p class="w-[11ch]"><span class="text-[rgb(1_156_222)]"><div>{props.BackData.accessories[2].sn}</div></span></p>):""}
                  {props.BackData.accessories.length > 2 ? (<p class="w-[13ch]"><span class="text-[rgb(1_156_222)]"><div>{props.BackData.accessories[2].impDate}</div></span></p>):""}
                  {props.BackData.accessories.length > 2 ? (<p class="w-[11ch]"><span class="text-[rgb(1_156_222)]"><div>{props.BackData.accessories[2].manufacturer}</div></span></p>):""}
              </div>
            </div>
            <div class="mt-[1%] text-card-copyright">
              <div>本人体内已植入上述型号产品。</div>
              <div>I have the above model of product implanted.</div>
            </div>
          </div>
          <div class="mt-[1%] text-card-copyright">
            <div>© 2022雅培版权所有。</div>
            <div>MAT-2206230 v1.0 | 项目仅审批在中国使用。</div>
          </div>
        </div>
        {/* </div> */}
      </ElectronicWarrantyCardFrame>
    </div>
  );
};
export default defineComponent(ElectronicwarrentyCardBack, {
  props: ["BackData","Status"],
});
