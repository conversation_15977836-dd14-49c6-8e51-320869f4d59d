import { defineComponent, FunctionalComponent, defineEmits } from 'vue';
import { Icon, Image } from 'vant';
// import logo from '../../assets/logo.png'
// import logo from '#/src/assets/logo.png'
import logo from '#/src/assets/logo.png';
import { useUserStore } from '#src/stores/index.js';
import { storeToRefs } from 'pinia';
import { useRoute, useRouter } from 'vue-router';

const Header: FunctionalComponent<{ class?: string; isShowBack?: boolean; onClickBack?: () => void }> = (
  props = { isShowBack: true },
  context
) => {
  // const isShowBack = props.isShowBack === true ? true : false;
  const router = useRouter();
  const route = useRoute();
  const isShowBack = props.isShowBack === undefined ? true : props.isShowBack;
  // debugger
  const { userInfo } = storeToRefs(useUserStore());
  const hiddenBackUrlList = ['home', 'error'];
  return () => (
    <div class=" sticky top-0 w-full z-[51] h-16 bg-white min-h-16  ">
      <div class="flex w-full h-full">
        <div class="flex-1 flex items-center px-4 ">
          {isShowBack && (
            <Icon
              class="pr-2"
              name="arrow-left"
              size={24}
              onClick={() => {
                if (props.onClickBack) {
                  props.onClickBack();
                  // context.emit('clickIconBack');
                } else {
                  router.back();
                }
              }}
            ></Icon>
          )}

          <div class="flex flex-col ">
            <Image class="w-24" src={logo}></Image>
            {route.name !== 'new' && route.query.surgeonNo && (
              <div class="text-gray-400 text-xs">手术编号：{route.query.surgeonNo}</div>
            )}
          </div>
        </div>
        <div class=" flex justify-end  items-center   pr-4">{userInfo?.value?.name}</div>
      </div>
      <div id="header"></div>
    </div>
  );
};

// export default defineComponent(Header, { props: ['class', 'isShowBack', 'onClickBack'],emits:['clickIconBack'] });
export default defineComponent(Header, { props: ['class', 'isShowBack', 'onClickBack'] });
