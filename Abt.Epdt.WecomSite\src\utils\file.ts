
export const base64ToFile = (base64String, fileName) =>{
    // 移除 data URL 的前缀部分（如果有）
    const arr = base64String.split(',');
    const mimeType = arr[0].match(/:(.*?);/)[1]; // 获取文件的 MIME 类型
    const binaryString = atob(arr[1]); // Base64 解码

    // 创建一个长度与二进制数据相同的数组
    const len = binaryString.length;
    const uint8Array = new Uint8Array(len);

    // 将 Base64 解码的字符串转为字节
    for (let i = 0; i < len; i++) {
        uint8Array[i] = binaryString.charCodeAt(i);
    }

    // 创建 Blob 对象
    const file = new Blob([uint8Array], { type: mimeType });

    // 如果需要一个 File 对象，可以这样生成（用于表单上传时可能会用到）
    const fileWithMeta = new File([file], fileName, { type: mimeType });

    return fileWithMeta; // 返回文件对象
}

