import { createApp } from "vue";
import "vant/lib/index.css";
import "./style.css";
import App from "./App.vue";
import Wrapper from '#src/components/Loading/wrapper';
// import App from "./App.tsx";
import router from "./router";
import { createPinia } from "pinia";
import { VueQueryPlugin } from "@tanstack/vue-query";
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'


 
const app = createApp(App);
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate)

app.use(router);
app.use(pinia);
app.use(VueQueryPlugin);
app.use(Wrapper);
app.mount("#app");
