/**
 * 比较两张图片的清晰度
 * @param {string} base64url1 - 第一张图片的 Base64 URL
 * @param {string} base64url2 - 第二张图片的 Base64 URL
 * @returns {Promise<{ clarity1: number, clarity2: number, result: string }>} - 返回包含清晰度评分和比较结果的对象
 */
export async function compareImageClarity(base64url1, base64url2) {
    /**
     * 加载图片并返回 Image 对象
     * @param {string} base64url - 图片的 Base64 URL
     * @returns {Promise<HTMLImageElement>}
     */
    function loadImage(base64url) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'Anonymous'; // 解决跨域问题
            img.onload = () => resolve(img);
            img.onerror = (err) => reject(new Error('图片加载失败'));
            img.src = base64url;
        });
    }

    /**
     * 计算图片的拉普拉斯方差作为清晰度评分
     * @param {HTMLImageElement} img - 加载后的图片对象
     * @returns {Promise<number>} - 清晰度评分
     */
    function computeClarity(img) {
        return new Promise((resolve, reject) => {
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data;

                // 转换为灰度图
                const gray = [];
                for (let i = 0; i < data.length; i += 4) {
                    const luminance = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                    gray.push(luminance);
                }

                // 应用拉普拉斯算子
                const laplacian = [];
                const width = canvas.width;
                const height = canvas.height;
                for (let y = 1; y < height - 1; y++) {
                    for (let x = 1; x < width - 1; x++) {
                        const idx = y * width + x;
                        const lap =
                            -1 * gray[(y - 1) * width + (x - 1)] +
                            -1 * gray[(y - 1) * width + x] +
                            -1 * gray[(y - 1) * width + (x + 1)] +
                            -1 * gray[y * width + (x - 1)] +
                            8 * gray[idx] +
                            -1 * gray[y * width + (x + 1)] +
                            -1 * gray[(y + 1) * width + (x - 1)] +
                            -1 * gray[(y + 1) * width + x] +
                            -1 * gray[(y + 1) * width + (x + 1)];
                        laplacian.push(lap);
                    }
                }

                // 计算方差
                const mean = laplacian.reduce((sum, val) => sum + val, 0) / laplacian.length;
                const variance = laplacian.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / laplacian.length;
                resolve(variance);
            } catch (error) {
                reject(new Error('清晰度计算失败'));
            }
        });
    }

    try {
        // 加载两张图片
        const [img1, img2] = await Promise.all([loadImage(base64url1), loadImage(base64url2)]);

        // 计算清晰度评分
        const [clarity1, clarity2] = await Promise.all([computeClarity(img1), computeClarity(img2)]);

        // 比较结果
        let comparison = '';
        if (clarity1 > clarity2) {
            comparison = '图片1更清晰';
        } else if (clarity1 < clarity2) {
            comparison = '图片2更清晰';
        } else {
            comparison = '两张图片清晰度相同';
        }

        return {
            clarity1: clarity1,
            clarity2: clarity2,
            result: comparison
        };
    } catch (error) {
        throw error;
    }
}
