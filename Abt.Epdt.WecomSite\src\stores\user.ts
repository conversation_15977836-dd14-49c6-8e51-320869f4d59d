import { defineStore } from 'pinia';
import { Ref, ref } from 'vue';

type UserInfoProps = {
  code: string;
  name: string;
  id: string;
  role: string;
  email: string;
  phone: string;
  roleid: string;
  istodo: boolean;
  isquery: boolean;
  isproxy: boolean;
  proxyemail: string;
  isexitstodolist: boolean;
  isLogin: boolean;
  Rolelist:Array<RoleModel>;
};

type RoleModel = {
  rolename: string;
  roleseq: number;
  roleid: string;
  istodo: boolean;
  isquery: boolean;
  isproxy: boolean;
  proxyemail: string;
}
export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<UserInfoProps>(undefined);

    // const updateUser = (newUserInfo: UserInfoProps) => {
    //   console.trace('updateUser');
    //   console.log(newUserInfo);
    //   userInfo.value = newUserInfo;
    // };

    const updateUser = (
      newUserInfo: Partial<UserInfoProps> | ((currentState: UserInfoProps | undefined) => Partial<UserInfoProps>)
    ) => {
      if (typeof newUserInfo === 'function') {
        // 如果传入的是回调函数，计算新值
        const updatedInfo = newUserInfo(userInfo.value);
        userInfo.value = { ...userInfo.value, ...updatedInfo };
      } else {
        // 如果直接传入值，合并更新
        userInfo.value = { ...userInfo.value, ...newUserInfo };
      }
    };

    return { userInfo, updateUser };
  },
  // { persist: true }
);
