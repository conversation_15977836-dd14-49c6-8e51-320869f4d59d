<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>

  <body>
    <iframe id="iframe" src="https://md-mpdt-d.abbott.com.cn/wecom/index.html#/auth" style="display: none"></iframe>
    <script>
      var iframe = document.getElementById('iframe');
      iframe.onload = function () {
        var data = {
          action: 'isLogin',
        };
        // 向iframe调用事件
        iframe.contentWindow.postMessage(JSON.stringify(data), 'https://md-mpdt-d.abbott.com.cn');
      };

      // 接收返回进行判断，请判断origin
      window.addEventListener(
        'message',
        function (e) {
          console.log(e);
          if (!e.origin.endsWith('abbott.com.cn')) return;
          if (!e.data) return;
          const res = JSON.parse(e.data);
          /**
             * action: 'isLoginResponse',
             * status: 200|401|500,
             * data: 200返回当前登录用户的信息，401返回登录url，500代表系统错误
             * 
             * 200 sample 结构
             * {
    "username": "LHJ",
    "userid": "4a82b76e-5e8b-ef11-ac21-0017fa09904f",
    "code": "33333",
    "useremail": "<EMAIL>",
    "isexitstodolist": true,
    "role": [
        {
            "phone": "15150395142",
            "roleseq": 10,
            "roleid": "920395a7-8a79-ef11-a670-0017fa04df9e",
            "rolename": "销售员",
            "istodo": true,
            "isquery": true,
            "ismy": true,
            "isproxy": false,
            "proxyemail": ""
        }
    ]
}
             */

          if (res.action === 'isLoginResponse') {
            //是否登录
            if (res.status === 200) {
              console.log('已登录');
              console.log(res.data);
            } else if (res.status === 401) {
              console.log('未登录');
              const url = res.data;
              //拿到的登录url是没有signonurl参数的，需要手动加上，signonurl是sso登录完成后转到的页面
              const loginUrl = `${url}&signonurl=${encodeURIComponent('https://www.bing.com')}`;
              //   window.location.href = loingUrl;
              console.log(loginUrl);
            } else {
              console.log('error');
              console.error(res.data);
            }
          }
        },
        false
      );
    </script>
  </body>
</html>
