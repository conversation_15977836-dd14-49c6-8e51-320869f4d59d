import request from '#src/service/request';
import { getLoginUrl, getLoginUrlWithoutRedirectParam } from '#src/utils/auth.ts';
import { defineComponent, FunctionalComponent, onMounted, onUnmounted } from 'vue';

const Auth: FunctionalComponent = () => {
  const messageEvent = async function (e) {
    if (!e.origin.endsWith('abbott.com.cn')) return;
    console.log(e);
    var data = JSON.parse(e.data);
    const responseData = { action: 'isLoginResponse', status: 200, data: undefined };
    if (data) {
      //   console.log(data);
      if (data.action == 'isLogin') {
        try {
          const res = await request({
            method: 'get',
            url: '/api/auth/me',
          });

          responseData.status = 200;
          responseData.data = res.data;
        } catch (error) {
          if (error.response.status == 401) {
            responseData.status = 401;
            responseData.data = getLoginUrlWithoutRedirectParam();
          } else {
            console.error(error);
            responseData.status = 500;
            responseData.data = error;
          }
        }
      }

      window.parent.postMessage(JSON.stringify(responseData), e.origin);
    }
  };
  onMounted(() => {
    window.addEventListener('message', messageEvent, false);
  });
  onUnmounted(() => {
    window.removeEventListener('message', messageEvent, false);
  });
  return () => <div>Auth</div>;
};

export default defineComponent(Auth);
