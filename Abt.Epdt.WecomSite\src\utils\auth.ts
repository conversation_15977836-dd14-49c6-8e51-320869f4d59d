export const getLoginUrl = (email?: string) => {
  const redirectBaseUrl = location.origin + location.pathname;
  const currentUrl = location.href;

  return `${import.meta.env.ONEPDT_PROXY_URL ?? location.origin}/api/auth/login?signonurl=${encodeURIComponent(
    currentUrl
  )}&errorurl=${encodeURIComponent(redirectBaseUrl + '#/error')}${email ? `&email=${encodeURIComponent(email)}` : ''}`;
};

export const getLoginUrlWithoutRedirectParam = () => {
  const redirectBaseUrl = location.origin + location.pathname;
  return `${import.meta.env.ONEPDT_PROXY_URL ?? location.origin}/api/auth/login?errorurl=${encodeURIComponent(
    redirectBaseUrl + '#/error'
  )}`;
};
