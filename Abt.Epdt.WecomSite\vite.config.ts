import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { VantResolver } from '@vant/auto-import-resolver';
import mkcert from 'vite-plugin-mkcert';
import { manualChunksPlugin } from 'vite-plugin-webpackchunkname';
import copyWasm from './src/plugins/copywasm';
// import { visualizer } from "rollup-plugin-visualizer";

// https://vitejs.dev/config/

export default ({ mode }) => {
  const isProduction = mode === 'production';

  return defineConfig({
    base: './',
    envPrefix: 'ONEPDT_',
    // esbuild: {
    //   drop: isProduction ? ['console', 'debugger'] : [],
    // },
    plugins: [
      // mkcert(),
      vue(),
      vueJsx({
        // options are passed on to @vue/babel-plugin-jsx
      }),
      AutoImport({
        resolvers: [VantResolver()],
      }),
      Components({
        resolvers: [VantResolver()],
      }),
      copyWasm(),
      // manualChunksPlugin()
      //  visualizer(),
    ],
    server: {
      host: 'localhost',
      port: 3000, //本机端口
      proxy: {
        '/api': {
          // target:import.meta.env
          target: 'https://localhost:5001/', //代理目标服务器地址
          secure: false,
        },
        '/Saml2': {
          target: 'https://localhost:5001/', //代理目标服务器地址
          secure: false,
        },
      },
    },
    build: {
      // modulePreload: {
      //     resolveDependencies: (url, deps, context) => {
      //       return [];
      //     }
      //   },
      rollupOptions: {
        output: {
          manualChunks: (id: string) => {
            if (id.indexOf('node_modules/jimp/') !== -1) {
              return 'jimp';
            }
            if (id.indexOf('node_modules/@techstark/opencv-js/') !== -1) {
              return 'opencv-js';
            }
            if (id.indexOf('node_modules/@tato30/vue-pdf/') !== -1) {
              return 'vue-pdf';
            }
          },
          //拆包
          // manualChunks:{
          //     '@techstark/opencv-js':['@techstark/opencv-js'],
          //     '@tato30/vue-pdf':['@tato30/vue-pdf'],
          //     'jimp':['jimp']
          // }
        },
      },
    },
  });
};
