import { Button, CellGroup, Checkbox, Field, Icon, showDialog, showToast, Tag, showFailToast } from 'vant';
import Section from './Section';
import { defineComponent, FunctionalComponent, onMounted, onUnmounted, ref, Ref, UnwrapRef, watch } from 'vue';
import request from '#src/service/request.js';
import { useRouter } from 'vue-router';
import { useGlobalSelect, useSelect } from '#src/hooks/useSelect.ts';
import { ImplantProduct } from '..';
import '#src/pages/Edit/components/components.css';
import $bus from '#src/mitt.js';
const ProductCard: FunctionalComponent<{
  // isShowSelect: Ref<UnwrapRef<boolean>>;
  // updateSelect: ReturnType<typeof useSelect>["updateSelect"];
  // ImplantProduct:
  onClose: (index) => void;
  product: ImplantProduct;
  index: number;
  disabled: boolean;
}> = (props) => {
  const router = useRouter();
  const select = useGlobalSelect();

  const serialNumberblur = async () => {
    console.log('update serial number');
    props.product.SAPProduct = [];
    props.product.productid = '';
    props.product.model = '';
    props.product.bigType = '';
    props.product.category = '';
    request({
      url: 'api/epdt/GetSAPProduct?name=' + props.product.serialNumber + '&isNonMainlandProduct='+(props.product.isNonMainlandProduct?props.product.isNonMainlandProduct:'false'),
      method: 'get',
    }).then((res) => {
      if (res.data&&res.data.length > 0) {
        props.product.SAPProduct = res.data;
        if (res.data.length == 1) {
          props.product.model = res.data[0].zmod;
          GetOnePDTProduct(res.data[0].zmod);
        }
      } else {
        showToast({ message: props.product.isNonMainlandProduct?'该产品型号信息不存在':'序列号当前不在产品库内', duration: 3000 });
      }
    }).catch((error)=>{
      showFailToast({
        message: error.response.data,
        duration: 3000,
      });
    });
  };

  watch(
    () => props.product.triggerUpdateInfo,
    (newValue, oldValue) => {
      if (newValue === true) {
        // debugger
        serialNumberblur();
      }
    }
  );

  onMounted(() => {
    console.log('regier card event');
    console.log(serialNumberblur)
    // debugger
    $bus.on('updateProductInfo', serialNumberblur);
  });

  onUnmounted(() => {
    console.log(' off event');
    $bus.off('updateProductInfo');
  });
  const GetOnePDTProduct = async (serialNumber) => {
    request({
      url: 'api/epdt/GetOnePDTProduct?name=' + serialNumber + '',
      method: 'get',
    }).then((res) => {
      console.log(res);
      if (res.data && res.data.id) {
        props.product.productid = res.data.id;
        //props.product.model = res.data.onepdt_name;
        props.product.bigType = res.data.onepdt_type;
        props.product.category = res.data.onepdt_category;
      } else {
        showToast({ message: '该产品信息不存在', duration: 3000 });
      }
    });
  };

  const DeleteProduct = async () => {
    showDialog({
      message: '是否确认删除?',
      showCancelButton: true,
    }).then(() => {
      props.onClose(props.index);
      if (props.product.id && props.product.id != '' && props.product.id != undefined) {
        request({
          url: 'api/epdt/Delete?entityname=onepdt_t_operation_implant&entityid=' + props.product.id,
          method: 'get',
        })
          .then(() => {
            $bus.emit('EditOperation', '删除成功');
          })
          .catch((error) => {
            showFailToast({
              message: error.response.data,
              duration: 3000,
            });
          });
      }
      showToast({ message: '删除成功' });
    });
  };

  const isChecked = ref(true);
  return () => (
    <div class="shadow-sm">
      <div class="flex">
        <Tag
          onClose={() => {
            console.log(props.product);
            DeleteProduct();
          }}
          size="large"
          class="ml-auto mr-4"
          type="primary"
          closeable={!props.disabled}
        >
          产品{props.index + 1}
        </Tag>
      </div>
      <CellGroup inset>
        <Field
          //   v-model={messageNote.value}
          label="序列号"
          // v-model:show={props.product.serialNumber}
          // modelValue={props.product.serialNumber}
          v-model={props.product.serialNumber}
          inputAlign="right"
          disabled={props.disabled}
          // v-slots={{
          //   button: () => (
          //     <div>
          //       <Icon
          //         onClick={() => {
          //           // 创建扫码对象
          //           // router.push({ name: "scan" });
          //         }}
          //         name="scan"
          //       />
          //     </div>
          //   ),
          // }}
          onBlur={serialNumberblur}
          //   type="textarea"
        />

        <Field
          border
          autocomplete="new-password"
          label="产品型号"
          disabled={props.disabled || (props.product.SAPProduct && props.product.SAPProduct.length <= 1)}
          readonly
          required
          id="productmodel"
          name="productmodel"
          errorMessageAlign="right"
          input-align="right"
          rules={[{ required: true, message: '请选择产品型号' }]}
          isLink
          modelValue={props.product.model}
          onClick={() => {
            if (!props.disabled && props.product.SAPProduct && props.product.SAPProduct.length > 1) {
              select.updateSelect({
                newSelectColumns: props.product.SAPProduct.map((item, index) => ({
                  text: item.zmod,
                  value: item.id,
                })),
                newOnSelectConfirm: (option) => {
                  console.log('option', option);
                  props.product.model = option.selectedOptions[0].text;
                  GetOnePDTProduct(option.selectedOptions[0].text);
                },
                newIsShowSearch: true,
                newSelectProps: {} as any,
              });
            }
          }}
        >
          {{
            button: () =>
              props.product.model && (!props.disabled && props.product.SAPProduct && props.product.SAPProduct.length > 1) ? (
                <div
                  style="cursor: pointer; color: #999; padding: 0 10px;"
                  onClick={(e) => {
                    e.stopPropagation(); // 阻止触发 onClick 的选择逻辑
                    props.product.model = '';
                    props.product.productid = '';
                    props.product.bigType = '';
                    props.product.category = '';
                  }}
                >
                  ✖
                </div>
              ) : null,
          }}
        </Field>

        {/* <Field
          label="产品型号"
          inputAlign="right"
          disabled={props.disabled}
          readonly
          modelValue={props.product.model}
        ></Field> */}

        <Field
          label="产品大类"
          inputAlign="right"
          disabled={props.disabled}
          // v-model:show={props.product.type}
          modelValue={props.product.category}
          readonly
        />

        <Field
          label="产品类别"
          inputAlign="right"
          disabled={props.disabled}
          // v-model:show={props.product.type}
          modelValue={props.product.bigType}
          readonly
        />
      </CellGroup>
      <Section title="附加信息" style="width:auto" collapse={true}>
        <div id="addBox" style="display:flex;">
          <Field
            label="临床试验"
            inputAlign="left"
            disabled={props.disabled}
            v-slots={{
              input: () => (
                <Checkbox
                  modelValue={props.product.isNonClinicalTrial}
                  disabled={props.disabled}
                  onClick={() => {
                    if (props.disabled == false) {
                      props.product.isNonClinicalTrial = !props.product.isNonClinicalTrial;
                    }
                  }}
                  iconSize={16}
                  shape="square"
                ></Checkbox>
              ),
            }}
          />

          <Field
            label="非大陆产品"
            inputAlign="left"
            disabled={props.disabled}
            v-slots={{
              input: () => (
                <Checkbox
                  modelValue={props.product.isNonMainlandProduct}
                  disabled={props.disabled}
                  onClick={() => {
                    if (props.disabled == false) {
                      props.product.isNonMainlandProduct = !props.product.isNonMainlandProduct;
                      serialNumberblur();
                    }
                  }}
                  iconSize={16}
                  shape="square"
                ></Checkbox>
              ),
            }}
          />
          <Field
            label="损耗产品"
            inputAlign="right"
            disabled={props.disabled}
            v-slots={{
              input: () => (
                <Checkbox
                  modelValue={props.product.isWearAndTear}
                  disabled={props.disabled}
                  onClick={(option) => {
                    if (props.disabled == false) {
                      props.product.isWearAndTear = !props.product.isWearAndTear;
                    }
                  }}
                  onChange={(option) => {
                    console.log('change');
                    console.log(option);
                  }}
                  iconSize={16}
                  shape="square"
                ></Checkbox>
              ),
            }}
          />
        </div>
      </Section>
    </div>
  );
};

export default defineComponent(ProductCard, {
  props: ['product', 'index', 'onClose', 'disabled'],
});
