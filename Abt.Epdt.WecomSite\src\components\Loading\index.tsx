import { Dialog, Loading, Popup } from 'vant';
import { defineComponent, FunctionalComponent, onMounted, provide, ref } from 'vue';

const OnePDTLaoding: FunctionalComponent<{ message?: string }> = (props) => {
  onMounted(() => {
  });
  return () => (
    <>
      <Popup show={true} position="top" overlay={false}>
        <Dialog show={true} showConfirmButton={false}>
          <div class="flex flex-col justify-center items-center p-2 h-40 text-black">
            <Loading></Loading>
            {props.message ?? '加载中'} 
          </div>
        </Dialog>
      </Popup>
    </>
  );
};

export default defineComponent(OnePDTLaoding, { props: ['message'] });
