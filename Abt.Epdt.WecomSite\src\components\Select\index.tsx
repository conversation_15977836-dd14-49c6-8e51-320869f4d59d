import { defineComponent, FunctionalComponent, onMounted, onUpdated, ref, Ref,onUnmounted,watch } from 'vue';
import { Field, Picker, PickerProps, Popup } from 'vant';

export type SelectProps = {
  isShowSelect: Ref<boolean>;
  isShowSearch: Ref<boolean>;
  searchText: Ref<string>;

  state: any;
  selectProps: InstanceType<typeof Picker>['$props'];
};

const Select: FunctionalComponent<SelectProps> = (props) => {
  let { isShowSelect, state, selectProps, searchText, isShowSearch } = props;
  const hasRun = ref(false);

  onMounted(() => {});
  onUpdated(() => {
    function wrapElement(targetElement) {
      if (hasRun.value) return;
      // 确保传入的元素是有效的
      if (!targetElement || !(targetElement instanceof HTMLElement)) {
        console.error('传入的目标元素无效。');
        return;
      }

      // 创建一个新的 div 元素
      const wrapperDiv = document.createElement('div');
      wrapperDiv.id = 'onepdt-wrapper'

      // 将新创建的 div 元素插入到目标元素的前面
      targetElement.parentNode.insertBefore(wrapperDiv, targetElement);

      // 将目标元素作为子元素放到新创建的 div 里面
      wrapperDiv.appendChild(targetElement);
      hasRun.value = true;
    }

    // 使用示例：传入需要包裹的元素
    // const targetElement = document.querySelector('.van-picker-column__wrapper');

   
  });

  return () => {
    return (
      <>
        <Popup v-model:show={isShowSelect.value} position="bottom">
          <div>
            {isShowSearch.value && (
              <Field
                placeholder="输入要搜索的选项"
                // v-model="data.keyWord"

                v-model={searchText.value}
                left-icon="search"
              />
            )}

            <Picker {...props.selectProps} optionHeight={44}></Picker>
            {/* <Picker columns={[{ text: "111", value: "111" }]}></Picker> */}
          </div>
        </Popup>
      </>
    );
  };
};

export default defineComponent(Select, {
  props: ['isShowSelect', 'searchText', 'isShowSearch', 'state', 'selectProps'],
});
