// import ElectronicwarrentyCard from "#src/components/ElectronicWarrantyCard/index.tsx";
import ElectronicwarrentyCard from '#src/components/ElectronicWarrantyCard/index.jsx';
import { Button, closeToast, showLoadingToast, showToast, showFailToast, NoticeBar, Overlay, Icon } from 'vant';
import { defineComponent, FunctionalComponent, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import request from '#src/service/request.js';
import router from '#src/router.js';
import { error } from 'console';
import $bus from '../../mitt.js';
import { editMode } from '#src/global.js';

export type ValidateError = {
  name: string;
  message: string;
}[];

//跟台id
var operationid: any = '';
//提示
var tip: any = '';
//预览类型
var previewtype: any = '';
//识别卡信息
var CardTypes = ref([]);
//业务审批状态
var Approvalstatus: any = '';

const PreviewCard: FunctionalComponent = () => {
  const router = useRouter();
  const route = useRoute();
  const isShowLandscapeCard = ref(false);

  onMounted(() => {
    operationid = route.query.operationid;
    tip = route.query.Tip;
    previewtype = route.query.previewtype;
    Approvalstatus = route.query.Approvalstatus;
    const queryObjString: any = route.query.CardTypes;
    CardTypes.value = JSON.parse(queryObjString);
    console.log('onMounted111', CardTypes);

    // setTimeout(() => {

    // }, 3000);
  });

  const showLandscapeCard = () => {
    isShowLandscapeCard.value = true;
    const card = document.getElementById('card-preview-front');
    const landscapeCard = document.getElementById('landscape-card-wrap') as HTMLDivElement;
    const cardRect = card.getBoundingClientRect();
    // 获取设备尺寸
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    // 获取原始卡片尺寸
    const cardWidth = cardRect.width;
    const cardHeight = cardRect.height;

    // 计算缩放比例
    // 考虑旋转90度后的尺寸
    const scaleX = screenHeight / cardWidth;
    const scaleY = screenWidth / cardHeight;
    const scale = Math.min(scaleX, scaleY) * 0.9; // 留出一些边距
    // 应用缩放

    let styleSheet = document.styleSheets[0]; // 获取第一个样式表
    styleSheet.insertRule(
      `#landscape-card-wrap { transform: rotate(90deg) scale( ${scale} ); }`,
      styleSheet.cssRules.length
    );
  };

  const SubmitOperation = async () => {
    showLoadingToast({
      duration: 0,
      message: '提交中...',
      forbidClick: true,
    });
    request({
      url: 'api/epdt/SubmitOperation?operationid=' + operationid,
      method: 'get',
    })
      .then((res) => {
        $bus.emit('EditOperation', '提交成功');
        $bus.emit('Todolist', '提交成功');
        router.push({ name: 'submit' });
        closeToast();
      })
      .catch((error) => {
        closeToast();
        showFailToast({
          message: error.response.data,
          duration: 3000,
        });
      });
  };

  return () => (
    <div class=" flex-1 relative bg-mainbg">
      <div class="p-4">
        <div class="title">设备识别卡预览</div>

        {CardTypes.value.length !== 0 ? (
          <ElectronicwarrentyCard
            verticalSwipe={false}
            id="card-preview"
            indicatorHeight="2.5rem"
            CardTypeList={CardTypes}
            Status={Approvalstatus}
            onClick={showLandscapeCard}
          ></ElectronicwarrentyCard>
        ) : (
          ''
        )}
        {CardTypes.value.length === 0 ? (
          <div style="height:200px;background-color: #f5f5f5;border: 1px solid #ddd;border-radius: 5px;padding: 15px;box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
            <div style="font-size: 15px;color: #666;text-align: center;margin-top:12%">根据雅培识别卡发放制度，</div>
            <div style="font-size: 15px;color: #666;text-align: center;">该患者植入产品暂不符合发放条件</div>
          </div>
        ) : (
          ''
        )}
        <div class="text-gray-400 text-sm p-2">
          <div>若识别卡预览信息与实际不符，您可以选择：</div>
          <div>1.返回，检查植入信息，修订后再提交</div>
          <div>2.返回，添加备注信息，以便后台人员处理</div>
          {/* <div>3:继续提交，邮件线下申请，业务审批人员处理</div> */}
        </div>
        {tip && tip != '' ? <div class="font-bold text-red-500 text-center">注：{tip}</div> : ''}
      </div>
      <div class="flex w-full py-2 px-6 bg-white mt-0 fixed bottom-0 gap-4">
        <Button
          class="flex-1"
          onClick={() => {
            $bus.emit('PreviewCardback', editMode.editMail);
            router.go(-1);
            // router.replace({
            //   name: 'edit',
            //   query: {
            //     type: 'todo',
            //     editMode: editMode.editMail,
            //     operationid: operationid,
            //     timetemp: new Date().getUTCMilliseconds(),
            //   },
            // });
          }}
        >
          返回
        </Button>
        {previewtype != "预览"?(<Button
          class="flex-1"
          type="primary"
          onClick={SubmitOperation}
        >
          提交
        </Button>):""}
      </div>
      <div></div>
      <Overlay show={isShowLandscapeCard.value}   class="  z-[1000] " lockScroll={false}>
        <div class=" w-screen h-screen relative flex  ">
          <Icon name="close" size={48}  class="fixed bottom-4 right-4 z-[2001] " onClick={() => (isShowLandscapeCard.value = false)}></Icon>
          <div id="landscape-card-wrap" class=" z-[2000]    w-full   origin-center   m-auto ">
            <ElectronicwarrentyCard
              verticalSwipe={true}
              id="landscape-card"
              indicatorHeight="0"
              CardTypeList={CardTypes}
              Status={Approvalstatus}
            ></ElectronicwarrentyCard>
          </div>
        </div>
      </Overlay>
    </div>
  );
};

export default defineComponent(PreviewCard);
