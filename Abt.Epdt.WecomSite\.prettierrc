{"singleQuote": true, "semi": true, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "arrowParens": "always", "printWidth": 120, "disableLanguages": ["vue"], "endOfLine": "auto", "eslintIntegration": false, "jsxBracketSameLine": false, "jsxSingleQuote": false, "stylelintIntegration": false, "trailingComma": "es5", "tslintIntegration": false, "ignorePath": ".giti<PERSON>re", "overrides": [{"files": "*.wxml", "options": {"parser": "html"}}, {"files": "*.wxss", "options": {"parser": "css"}}, {"files": "*.wxs", "options": {"parser": "babel"}}], "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}