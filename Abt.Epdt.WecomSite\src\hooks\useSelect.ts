import { Picker } from "vant";
import { inject, ref, Ref, UnwrapRef } from "vue";
export type UpdateSelect = (props: {
  newSelectColumns: { text: string; value: string }[];
  newOnSelectConfirm: (...args) => void;
  newIsShowSearch?: boolean;
}) => void;
// {
//   isShowSelect: Ref<UnwrapRef<boolean>>;

//   isShowSearch: Ref<UnwrapRef<boolean>>;
//   searchText: Ref<UnwrapRef<string>>;
//   selectColumns: Ref<UnwrapRef<{ text: string; value: string }[]>>;
//   onSelectConfirm: Ref<UnwrapRef<(options: any) => void>>;
//   updateSelect: UpdateSelect;
// };

export const useSelect = (
  isShowSelect?: Ref<UnwrapRef<boolean>>,
  selectColumns?: Ref<UnwrapRef<{ text: string; value: string }[]>>,
  onSelectConfirm?: Ref<UnwrapRef<(options: any) => void>>,
  isShowSearch?: Ref<UnwrapRef<boolean>>,
  searchText?: Ref<UnwrapRef<string>>,
  selectProps?: Ref<InstanceType<typeof Picker>["$props"]>
) => {
  if (!isShowSelect) {
    isShowSelect = ref(false);
  }
  if (!selectColumns) {
    selectColumns = ref([]);
  }
  if (!onSelectConfirm) {
    onSelectConfirm = ref(() => {});
  }
  if (!isShowSearch) {
    isShowSearch = ref(false);
  }
  if (!searchText) {
    searchText = ref("");
  }
  if (!selectProps) {
    selectProps = ref({});
  }

  const updateSelect = (props: {
    newSelectColumns: (typeof selectColumns)["value"];
    newOnSelectConfirm: (...args) => void;
    newIsShowSearch?: boolean;
    newSelectProps?: InstanceType<typeof Picker>["$props"];
  }) => {
    const {
      newIsShowSearch,
      newOnSelectConfirm,
      newSelectColumns,
      newSelectProps,
    } = props;
    isShowSelect.value = true;
    onSelectConfirm.value = newOnSelectConfirm;
    selectColumns.value = newSelectColumns;
    isShowSearch.value = newIsShowSearch ?? false;
    selectProps.value = newSelectProps;
  };
  return {
    isShowSelect,
    isShowSearch,
    searchText,
    selectColumns,
    onSelectConfirm,
    updateSelect,
    selectProps
  };

  // return [isShow, selectColumns, onSelectConfirm, updateSelect] as [
  //   Ref<UnwrapRef<boolean>>,
  //   Ref<UnwrapRef<typeof selectColumns>>,
  //   typeof onSelectConfirm,
  //   (newSelectColumns: (typeof selectColumns)["value"]) => void
  // ];
};

export const useGlobalSelect = () => {
  const select = inject("select");
  return select as ReturnType<typeof useSelect>;
};