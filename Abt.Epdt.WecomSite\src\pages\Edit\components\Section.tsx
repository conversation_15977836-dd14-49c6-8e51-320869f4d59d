import { Badge, CellGroup, Divider, Icon } from "vant";
import {
  defineComponent,
  FunctionalComponent,
  ref,
  useSlots,
  defineExpose,
} from "vue";

const Section: FunctionalComponent<{ title: string; badgeCount?: number;collapse?:boolean }> = (
  props,
  context
) => {
  const slots = useSlots();
  const isCollapse = ref(props.collapse);
  // console.log(context);
  (context as any).expose({
    openSection: () => {
      isCollapse.value = false;
    },
  });
  // defineExpose({
  // openSection: () => {
  // isCollapse.value = false;
  // },
  // });

  // console.log(slots);
  return () => (
    <CellGroup inset class="shadow-md">
      <div class="p-4 pt-4 pb-2 w-full font-bold flex">
        <div class="flex gap-2 items-center label relative">
          <div class="text-sm text-abbott font-bold">{props.title}</div>
          {!!props.badgeCount && props.badgeCount !== 0 && (
            <div class="text-xs text-gray-400 flex items-end">
              产品数量：
              <Badge class="translate-x-0" content={props.badgeCount}></Badge>
            </div>
          )}
        </div>

        <div class="flex items-center text-xs text-gray-400 font-normal">
          {/* 产品数量:4 */}
        </div>
        {/* <Divider></Divider> */}
        <div
          class="ml-auto text-xs font-normal flex items-center text-gray-400"
          onClick={() => {
            isCollapse.value = !isCollapse.value;
          }}
        >
          {isCollapse.value ? "展开" : "收起"}
          &nbsp;
          <Icon
            class={isCollapse.value ? "-rotate-90" : "rotate-90"}
            size="12"
            name="arrow-double-left"
          ></Icon>
        </div>
      </div>

      <div
        class={`${
          isCollapse.value ? "baseinfo-hidden" : "baseinfo-show"
        }  overflow-hidden min-h-0 baseinfo `}
      >
        <div class="min-h-0 overflow-hidden">{() => slots.default()}</div>
      </div>
    </CellGroup>
  );
};

export default defineComponent(Section, { props: ["title", "badgeCount","collapse"] });
