import Card from '#src/components/Card/index.jsx';
import { editMode } from '#src/global.js';
import { Empty, List, Loading } from 'vant';
import { defineComponent, FunctionalComponent, onActivated, ref, Ref, Teleport } from 'vue';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import { SurgicalAssistantListProp } from '../../Search';
import { useKeepScroll } from '#src/hooks/useKeepScroll.js';

const TodoList: FunctionalComponent<{
  loading: Ref<boolean>;
  finished: Ref<boolean>;
  onListLoad: (...args: any[]) => any;
  surgicalAssistantList: Ref<any[]>;
}> = ({ loading, onListLoad, surgicalAssistantList, finished }) => {
  const router = useRouter();
  const listRef = ref();
  //   console.log(listRef.value)
  // useKeepScroll(listRef);

  return () => (
    // <div ref={listRef} class="overflow-y-scroll ">
    <div class=" overflow-visible ">
      <List
        ref={listRef}
        class=""
        v-model:loading={loading.value}
        onLoad={onListLoad}
        finished={finished.value}
        loadingText="加载中..."
      >
        <div class=" overflow-y-scroll flex flex-col gap-4 px-4 py-4">
          {!loading.value && surgicalAssistantList.value.length === 0 && <Empty imageSize={128} description='暂无数据'></Empty>}
          {surgicalAssistantList.value.map((item) => (
            <Card
              key={item.onepdt_name}
              onClick={(item) => {
                router.push({
                  name: 'edit',
                  query: {
                    type: 'todo',
                    editMode: editMode.editMail,
                    operationid: item.id,
                    timetemp: new Date().getUTCMilliseconds(),
                  },
                });
              }}
              item={{
                ...item,
                approveStatus: item.onepdt_approval_status_label,
                rejectReason: item.onepdt_comment,
                eventType: item.onepdt_submit_status_label,
              }}
            ></Card>
          ))}
        </div>
      </List>
    </div>
  );
};

export default defineComponent(TodoList, {
  props: ['loading', 'onListLoad', 'surgicalAssistantList', 'finished'],
});
