import { computed, defineComponent, FunctionalComponent, onMounted, ref, Teleport, watchEffect, watch } from 'vue';
import Filter from '../Search/components/Filter';
import { FilterProps, SurgicalAssistantListProp } from '../Search';
import { Button, Icon, Tabs, Tab, List, Loading, Empty, showFailToast, Dialog } from 'vant';
import { useRouter } from 'vue-router';
import { editMode } from '#src/global.js';
import dayjs from 'dayjs';
import TodoList from './components/TodoList';
import request from '#src/service/request.ts';
import Header from '#src/components/Header/index';
import $bus from '../../mitt.ts';
import { useKeepScroll } from '#src/hooks/useKeepScroll.js';
import { useGlobalSelect, useSelect } from '#src/hooks/useSelect.ts';
import { useParamsStore, useUserStore } from '#src/stores/index.ts';
import { storeToRefs } from 'pinia';

var typesDot = ref<any>([]);
const eventType = ['周报', '审批驳回', '邮寄复核', '信息更正', '退回重建'];

const Todo: FunctionalComponent = () => {
  const router = useRouter();
  const isShowFilter = ref(true);
  const isShowCalendar = ref(false);
  const implantDateRange = ref<[string, string]>(undefined);
  const hospital = ref<string>(undefined);
  const status = ref<string>(undefined);
  const implantType = ref<string>(undefined);
  const pageSize = ref(50);
  const pageIndex = ref(1);
  const listRef = ref<HTMLDivElement>();
  const filterParams = ref<FilterProps>({
    searchText: '',
    hospital: '',
    status: '',
    implantType: '',
    implantDateRange: undefined,
  });
  const active = ref(0);
  const error = ref(false);
  const finished = ref(false);
  const loading = ref(false);
  const totalDataCount = ref(0);
  var surgicalAssistantList = ref([]);
  // 获取子组件的 ref
  const filterRef = ref(null);

  useKeepScroll(listRef);

  const { userInfo } = storeToRefs(useUserStore());
  const userStore = useUserStore();

  onMounted(() => {
    $bus.on('Todolist', (value) => {
      surgicalAssistantList.value = [];
      pageIndex.value = 1;
      finished.value = false;
      fetchSurgicalAssistantList();
      LoginAndGetRole();
    });
  });

  const LoginAndGetRole = async () => {
    request({
      method: 'get',
      url: '/api/epdt/LoginAndGetRole?email=' + userInfo.value.email,
    })
      .then((res) => {
        if (res.data.role && res.data.role.length > 0) {
          userStore.updateUser({
            ...userInfo.value,
            isexitstodolist: res.data.isexitstodolist,
          });
        }
      })
      .catch((error) => {
        showFailToast({
          message: error.response.data,
          duration: 3000,
        });
        console.log(error);
      });
  };

  watch(
    () => userInfo.value?.isLogin,
    (newValue) => {
      if (newValue === true) {
        fetchSurgicalAssistantList();
      }
    }
  );

  const fetchSurgicalAssistantList = async () => {
    // debugger
    if (!userInfo.value?.isLogin) return;
    if (finished.value) return;
    loading.value = true;
    var tabtype = eventType[active.value];
    var tabtypevalue = eventType.indexOf(tabtype);
    var starttime = '';
    var endtime = '';
    if (filterParams.value.implantDateRange != undefined) {
      starttime = filterParams.value.implantDateRange[0];
      endtime = filterParams.value.implantDateRange[1];
    }

    // isShowLoading.value = true;
    totalDataCount.value = 0;
    request({
      url:
        'api/epdt/GetTodoList?email=' +
        userInfo.value.email +
        '&type=' +
        tabtypevalue +
        '&pagesize=' +
        pageSize.value +
        '&pageindex=' +
        pageIndex.value +
        '&searchText=' +
        filterParams.value.searchText +
        '&implantType=' +
        filterParams.value.implantType +
        '&status=' +
        filterParams.value.status +
        '&hospital=' +
        filterParams.value.hospital +
        '&starttime=' +
        starttime +
        '&endtime=' +
        endtime +
        '',
      method: 'get',
    })
      .then((res) => {
        console.log(res.data.datas);
        // loading.value = false;
        let xDatas = res.data.datas;
        typesDot.value = res.data.types;

        if (xDatas === null) {
          finished.value = true;
          return;
        }
        if (xDatas.length < pageSize.value) {
          finished.value = true;
        }
        // surgicalAssistantList.value = [];
        surgicalAssistantList.value.push(...xDatas);
        totalDataCount.value = res.data.total;
        pageIndex.value = pageIndex.value + 1;
      })
      .catch((error) => {
        showFailToast({
          message: error.response.data,
          duration: 3000,
        });
        finished.value = true;
      })
      .finally(() => {
        loading.value = false;
        // finished.value = true;
      });
  };

  watch(
    () => userInfo.value?.email,
    () => {
      pageIndex.value = 1;
      finished.value = false;
      fetchSurgicalAssistantList();
    }
  );

  return () => (
    <div class="flex-1 flex  flex-col" style="height: 100%">
      <div class="sticky top-0 ">
        <Header
          isShowBack={true}
          onClickBack={() => {
            router.replace({ name: 'landingpage' });
          }}
        ></Header>
        <div class=" sticky top-0 z-20 pb-2 flex flex-col bg-white">
          <div class="flex p-2 bg-white text-gray-400 sticky top-0 text-sm">
            <Button
              class="mx-2"
              size="small"
              type="primary"
              onClick={() => {
                router.push({
                  name: 'edit',
                  query: {
                    type: 'create',
                    operationid: '',
                    editMode: editMode.new,
                    timetemp: new Date().getUTCMilliseconds(),
                  },
                });
              }}
            >
              创建
            </Button>
            <Button
              class="mx-2"
              size="small"
              type="primary"
              onClick={() => {
                surgicalAssistantList.value = [];
                pageIndex.value = 1;
                finished.value = false;
                fetchSurgicalAssistantList();
              }}
            >
              搜索
            </Button>
            <Button
              class="mx-2"
              size="small"
              type="primary"
              onClick={() => {
                if (filterRef.value) {
                  filterRef.value.resetFilterParams(); // 调用子组件暴露的方法
                }
              }}
            >
              清除
            </Button>
            <div
              class="flex ml-auto mr-2 justify-center items-center sticky top-0"
              onClick={() => {
                isShowFilter.value = !isShowFilter.value;
              }}
            >
              <div>记录条数：{totalDataCount.value}</div>
              <Icon name="filter-o" />
              &nbsp;筛选 &nbsp;
              <Icon class={isShowFilter.value ? 'rotate-90' : '-rotate-90'} size="12" name="arrow-double-left"></Icon>
            </div>
          </div>

          <div class={`${!isShowFilter.value ? 'baseinfo-hidden' : 'baseinfo-show'}   min-h-0 baseinfo `}>
            <Filter
              ref={filterRef}
              status={status}
              filterParams={filterParams}
              hospital={hospital}
              implantDateRange={implantDateRange}
              implantType={implantType}
              isShowCalendar={isShowCalendar}
              PageType={ref('待办')}
            ></Filter>
          </div>
          <Tabs
            v-model:active={active.value}
            onChange={(e) => {
              active.value = e;
              surgicalAssistantList.value = [];
              pageIndex.value = 1;
              finished.value = false;
              fetchSurgicalAssistantList();
            }}
          >
            {eventType.map((event) => (
              <Tab title={event} dot={typesDot.value.find((t) => t.typename == event)?.ishowdot}></Tab>
            ))}
          </Tabs>
        </div>
      </div>{' '}
      <div
        // ref="ListDom"
        ref={listRef}
        style={{
          // height: `calc(100% - ${isShowFilter.value ? '376px' : '228px'})`,
          'margin-bottom': '60px',
          overflow: 'hidden',
          overflowY: 'scroll',
        }}
      >
        <TodoList
          loading={loading}
          onListLoad={fetchSurgicalAssistantList}
          surgicalAssistantList={surgicalAssistantList}
          finished={finished}
        ></TodoList>
      </div>
      <div id="todoloading"></div>
    </div>
  );
};

// <div ref="ListDom" :style="{height:calc(100% - 228px);overflow: hidden; overflow-y: scroll;}">
// 312px 64    376px     isShowFilter.value
export default defineComponent(Todo);
