import { defineStore } from 'pinia';
import { UploaderFileListItem } from 'vant';
import { ref } from 'vue';

// export const ACTION = {
//   SEND_TO_BARCODE: 'sendToBarcode',
//   BACK_TO_EDIT: 'backToEdit',
//   UNDEFINED: undefined,
// };
export enum ACTION {
  SEND_TO_BARCODE,
  BACK_TO_EDIT,
  UNDEFINED,
}

type ReceiptFormProps = {
  action: ACTION;
  fileData: UploaderFileListItem & { data?: any }[];
  barcodeArr: string[];
  option?: {
    isDisabled: boolean;
  };
};
export const useReceiptFormStore = defineStore('receiptForm', () => {
  const receiptForm = ref<ReceiptFormProps>({
    fileData: [],
    barcodeArr: [],
    action: ACTION.UNDEFINED,
  });

  const updateReceiptForm = (newRceiptForm: ReceiptFormProps) => {
    receiptForm.value = newRceiptForm;
  };
  return { receiptForm, updateReceiptForm };
});
