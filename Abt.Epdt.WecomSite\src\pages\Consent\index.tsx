import Header from '#src/components/Header';
import router from '#src/router.js';
import request from '#src/service/request';
import { useUserStore } from '#src/stores/user.js';
import { isVantDialogCancel } from '#src/utils/basic.js';
import { useMutation, useQuery } from '@tanstack/vue-query';
import { userInfo } from 'os';
import { storeToRefs } from 'pinia';
import './index.css';
import { Checkbox, Dialog, Icon, Loading, showDialog, showToast } from 'vant';
import { computed, defineComponent, FunctionalComponent, onMounted, onUnmounted, ref, watch, watchEffect } from 'vue';

const Consent: FunctionalComponent = () => {
  const isCancelConfirm = ref(false);
  const isLoading = ref(false);
  const isShowDialog = ref(false);
  const policy = ref<any[]>([]);
  const currentPolicyIndex = ref(0);
  const confirm1Checkbox = ref(false);
  const confirm2Checkbox = ref(false);
  const isConfirmEnabled = computed(() =>  confirm1Checkbox.value && confirm2Checkbox.value);


  const userStore = useUserStore();
  const { userInfo } = storeToRefs(useUserStore());

  const checkAndUpdateUserPolicy = async () => {
    const policyActionMapping = {
      '200000': (res) => {
        //已经是最新政策
        router.push({ name: 'landingpage' });
      },
      '200003': async (res) => {
        console.log(res);
        const policyArr = JSON.parse(res.data);
        await getPolicy(policyArr);
        // debugger;
        //
      },
      '200004': async (res) => {
        //当前用户暂无授权
        await getPolicy();
      },
    };
    try {
      const result = await checkCurrentUserPolicy();
      policyActionMapping[result.data.code](result.data);
      console.log(result);
    } catch (error) {}
  };

  const updatePolicy = async (policyId) => {
    await request({
      url: `/api/policy/ConsentByUser`,
      method: 'POST',
      data: {
        type: 1,
        applicationID: import.meta.env.ONEPDT_POLICY_APP_ID,
        userID: userInfo.value.code,
        policyID: policyId,
      },
    });
  };

  const checkCurrentUserPolicy = async () => {
    const result = await request({
      url: `/api/policy/CheckPolicyByUser?applicationID=${import.meta.env.ONEPDT_POLICY_APP_ID}&type=1&id=${
        userInfo.value.code
      }`,
    });
    return result;
  };

  const getPolicy = async (policyid?: string[]) => {
    isLoading.value = true;
    const params = new URLSearchParams();
    params.append('applicationID', import.meta.env.ONEPDT_POLICY_APP_ID);
    if (policyid) {
      policyid.forEach((id) => params.append('policyID', id));
    }

    const policyData = await request({
      url: '/api/policy/GetPolicies',
      params: params,
      // params: policyid ? { applicationID: appId, policyID: policyid } : { applicationID: appId },
    });
    isLoading.value = false;
    console.log(policyData);
    if (policyData.data.code == '200000') {
      const policyArr = JSON.parse(policyData.data.data).filter((policy) => policy.userTypeValue === 1);
      console.log(policyArr);
      policy.value = policyArr;
      isShowDialog.value = true;
      // for (let i = 0; i < policyArr.length; i++) {
      //   console.log(i);
      //   const policy = policyArr[i];

      //   isLoading.value = false;
      //   isShowDialog.value = true;
      //   title.value = policy.privacyName;
      //   message.value = policy.privacyContent;

      // }
    }
  };

  onMounted(() => {
    if (userInfo.value) {
      checkAndUpdateUserPolicy();
      // checkCurrentPolicy();
    }

    // getData();
  });

  watch(
    () => userInfo.value?.isLogin,
    (newValue) => {
      if (newValue === true) {
        checkAndUpdateUserPolicy();
        // fetchSurgicalAssistantList();
      }
    }
  );

  // watchEffect(()=>{
  // if(confirm1Checkbox.value&&confirm2Checkbox.value){

  // }
  // })
  // console.log(isConfirmDiabled.value && confirm1Checkbox.value && confirm2Checkbox.value)

  return () => {
    return (
      <div class="flex flex-col  h-screen">
        <Header isShowBack={false}></Header>
        <div class="flex-1 flex flex-col justify-center items-center">
          {isLoading.value && (
            <Loading size={48} vertical>
              检查政策授权中...
            </Loading>
          )}
          {isCancelConfirm.value && (
            <div class="flex flex-col justify-center items-center gap-4">
              <div>
                <Icon name="close" color="red" size={64} />
              </div>
              <div class="text-gray-500 text-md">授权已取消，请关闭页面</div>
            </div>
          )}
        </div>
        <>
          {policy.value.length > 0 && (
            <Dialog
              confirmButtonText="已阅读并同意"
              cancelButtonText="取消"
              title={policy.value[currentPolicyIndex.value].privacyName}
              confirmButtonDisabled={!isConfirmEnabled.value}
              // confirmButtonDisabled={isConfirmDiabled.value }
              showCancelButton={true}
              onCancel={() => {
                isCancelConfirm.value = true;
                isLoading.value = false;
              }}
              onConfirm={() => {
                const currentPolicy = policy.value[currentPolicyIndex.value];
                updatePolicy(currentPolicy.policyID);
                if (currentPolicyIndex.value === policy.value.length - 1) {
                  router.push({ name: 'landingpage' });
                } else {
                  document.querySelector('.van-dialog__message').scrollTop = 0;
                  currentPolicyIndex.value = currentPolicyIndex.value + 1;
                }
              }}
              message={() => (
                <div>
                  <div v-html={policy.value[currentPolicyIndex.value].privacyContent}></div>
                  <div class="flex flex-col gap-1 text-left">
                    <Checkbox
                      modelValue={confirm1Checkbox.value}
                      onClick={() => {
                        confirm1Checkbox.value = !confirm1Checkbox.value;
                      }}
                    >
                      1) 按照上述方式处理我的个人信息。
                    </Checkbox>
                    <Checkbox
                      modelValue={confirm2Checkbox.value}
                      onClick={() => {
                        confirm2Checkbox.value = !confirm2Checkbox.value;
                      }}
                    >
                      2) 为如上所述目的处理本人的敏感个人信息。
                    </Checkbox>
                  </div>
                </div>
              )}
              // v-slots={{footer:<div>12312321</div>}}
              allowHtml={true}
              v-model:show={isShowDialog.value}
            ></Dialog>
          )}
        </>
      </div>
    );
  };
};

export default defineComponent(Consent);
