import { defineComponent, FunctionalComponent,onMounted,ref } from "vue";
import { Cell, CellGroup, Image, showToast,showFailToast,Field,Popup,Button } from "vant";
import { useUserStore } from "#src/stores/index.js";
import { storeToRefs } from "pinia";
import { useGlobalSelect } from "#src/hooks/useSelect.js";
import Header from "#src/components/Header/index";
import request from "#src/service/request.js";
import { VuePDF, usePDF } from '@tato30/vue-pdf'
import '@tato30/vue-pdf/style.css'

var userfilebase64= ref<string>();
const userfileurl = ref<string>();
const pdfSrc = ref<string>();
const numPages = ref<number>(1);

const page = ref(1)
 
const FileView: FunctionalComponent = () => {
  const userStore = useUserStore();
  const select = useGlobalSelect();
  const userInfo = storeToRefs(userStore);
  const { pdf ,pages } = usePDF( import.meta.env.ONEPDT_API_URL+'/api/epdt/GetUserManual')
  onMounted(() => {
    //GetUserManual();
    console.log("request.ONEPDT_API_URL",import.meta.env.ONEPDT_API_URL);
    console.log("request.baseURL",request.defaults.baseURL);
  });

  return () => (
    <div class="flex flex-col h-full  w-full bg-white ">
      <Header></Header>
      <div>          
          <VuePDF pdf={pdf.value} page={page.value} fit-parent/>
          <div class="flex p-2 bg-white text-gray-400 " style="justify-content: center;">
          <Button class="mx-2" size="small" type="primary" onClick={()=>{page.value = page.value > 1 ? page.value - 1 : page.value}}>Prev</Button>
          <span>{ page.value } / { pages.value }</span>
          <Button class="mx-2" size="small" type="primary"  onClick={()=>{page.value = page.value < pages.value ? page.value + 1 : page.value}}>Next</Button>
          </div>
      </div>
    </div>
  );
};

export default defineComponent(FileView);
