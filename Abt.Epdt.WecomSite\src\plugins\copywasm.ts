import copy from 'rollup-plugin-copy';
export default function copyWASMPlugin(): ReturnType<typeof copy> {
  return copy({
    verbose: true,
    hook: 'closeBundle',
    targets: [
      {
        src: ['src/utils/barcodewasm/**/*', '!src/utils/barcodewasm/_framework'],
        dest: 'dist/assets',
      },
      {
        src: 'src/utils/barcodewasm/_framework/**/*',
        dest: 'dist/assets',
      },
    ],
  });
}
