import { defineComponent, FunctionalComponent, ref, Ref, Fragment, onMounted } from 'vue';
import ElectronicwarrentyCardFront from './Front';
import ElectronicwarrentyCardBack from './Back';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Pagination, Navigation } from 'swiper/modules';
import './index.css';

import 'swiper/css';

import 'swiper/css/pagination';
import 'swiper/css/navigation';

const ElectronicwarrentyCard: FunctionalComponent<
  {
    CardTypeList: Ref<any[]>;
    Status: Ref<string>;
    indicatorHeight: string;
    id: string;
    verticalSwipe: boolean;
  },
  { click: () => void }
> = ({ CardTypeList, Status, indicatorHeight, id, verticalSwipe }) => {
  console.log('CardTypeList', CardTypeList.value);
  console.log('Status', Status);

  onMounted(() => {
    // setTimeout(()=>{
    //   console.log(refSwiper.value.updateAutoHeight)
    // },2000)
  });

  return () => (
    <div class="text-card" id={id} dir="ltr">
      {/* <div class="my-4">
          <span class="font-bold">识别卡类型：</span> {CardTypeList.value.length !== 0? CardTypeList.value[0].cardType:""}
        </div> */}
      <Swiper
        // ref={refSwiper}
        // direction={verticalSwipe ? 'vertical' : 'horizontal'}
        pagination={ { enable:true } as any}
        navigation={verticalSwipe? { enable: false } as any:undefined}
        spaceBetween={30}
        modules={[Pagination,Navigation]}
        autoHeight
      >
        {CardTypeList.value.length !== 0
          ? CardTypeList.value.map((item) => {
              return (
                <Fragment>
                  <SwiperSlide>
                    <ElectronicwarrentyCardFront id={id} FontData={item} Status={Status}></ElectronicwarrentyCardFront>
                  </SwiperSlide>
                  <SwiperSlide>
                    <ElectronicwarrentyCardBack BackData={item} Status={Status}></ElectronicwarrentyCardBack>
                  </SwiperSlide>
                </Fragment>
              );
            })
          : ''}
        <div class={`h-[${indicatorHeight}]`}></div>
      </Swiper>
    </div>
  );
};
export default defineComponent(ElectronicwarrentyCard, {
  props: ['CardTypeList', 'Status', 'indicatorHeight', 'id', 'verticalSwipe'],
});
