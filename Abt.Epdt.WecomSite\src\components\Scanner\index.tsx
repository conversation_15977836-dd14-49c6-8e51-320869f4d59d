import { defineComponent, FunctionalComponent, onMounted, ref } from "vue";
import jsQR from "jsqr";

const Scanner: FunctionalComponent = () => {
  onMounted(() => {
    setupp();
  });

  const responsive = ref(false);
  const previousCode = ref(null);
  const parity = ref(0);
  const active = ref(false);
  const canvas = ref();
  const video = ref();
  const showPlay = ref(false);

  const handleSuccess = (stream) => {
    // console.log(video.value);
    if (video.value.srcObject !== undefined) {
      video.value.srcObject = stream;
    } else if ((window as any).videoEl.mozSrcObject !== undefined) {
      video.value.mozSrcObject = stream;
    } else if (window.URL.createObjectURL) {
      video.value.src = window.URL.createObjectURL(stream);
    } else if (window.webkitURL) {
      video.value.src = window.webkitURL.createObjectURL(stream);
    } else {
      video.value.src = stream;
    }
    video.value.playsInline = true;

    const playPromise = video.value.play();
    playPromise.catch(() => (showPlay.value = true));
    playPromise.then(run);
  };

  const tick = () => {
    if (
      video.value &&
      video.value.readyState === video.value.HAVE_ENOUGH_DATA
    ) {
      //   canvas.value.height = videoWH.value.height;
      //   canvas.value.width = videoWH.value.width;
      const ctx = canvas.value.getContext("2d");
      canvas.value.height = 500;
      canvas.value.width = 500;
      console.log(canvas.value.drawImage)
      ctx.drawImage(
        video.value,
        0,
        0,
        500,
        500
        // this.$refs.canvas.width,
        // this.$refs.canvas.height
      );
      const imageData = ctx.getImageData(
        0,
        0,
        500,
        500
        // this.$refs.canvas.width,
        // this.$refs.canvas.height
      );
      let code = false;
      try {
        let result = jsQR(imageData.data, imageData.width, imageData.height);
        console.log(result);
      } catch (e) {
        console.error(e);
      }
      //   if (code) {
      //     this.drawBox(code.location);
      //     this.found(code.data);
      //   }
    }
    run();
  };

  const setupp = () => {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      previousCode.value = null;
      parity.value = 0;
      active.value = true;
      //   canvas = $refs.canvas.getContext("2d");
      //   const facingMode = this.useBackCamera ? { exact: "environment" } : "user";
      //   const handleSuccess = (stream) => {
      //     if (video.value.srcObject !== undefined) {
      //       video.value.srcObject = stream;
      //     } else if (window.videoEl.mozSrcObject !== undefined) {
      //       video.value.mozSrcObject = stream;
      //     } else if (window.URL.createObjectURL) {
      //       video.value.src = window.URL.createObjectURL(stream);
      //     } else if (window.webkitURL) {
      //       video.value.src = window.webkitURL.createObjectURL(stream);
      //     } else {
      //       video.value.src = stream;
      //     }
      //     video.value.playsInline = true;
      //     const playPromise = video.value.play();
      //     playPromise.catch(() => (this.showPlay = true));
      //     playPromise.then(this.run);
      //   };
      navigator.mediaDevices
        .getUserMedia({ video: { facingMode: "user" } })
        .then((stream) => {
          //   console.log("succ");
          handleSuccess(stream);
        })
        .catch(() => {
          navigator.mediaDevices
            .getUserMedia({ video: true })
            .then(handleSuccess)
            .catch((error) => {
              console.error(error);
              //   this.$emit("error-captured", error);
            });
        });
    }
  };
  const run = () => {
    if (active.value) {
      requestAnimationFrame(tick);
    }
  };
  return () => (
    <div>
      123213
      <div class="scaner" ref="scaner">
        <div class="banner" id="showBanner">
          {/* <i class="close_icon" @click="() => showBanner = false"></i> */}
          <p class="text">若当前浏览器无法扫码，请切换其他浏览器尝试</p>
        </div>
        <div class="cover">
          <p class="line"></p>
          <span class="square top left"></span>
          <span class="square top right"></span>
          <span class="square bottom right"></span>
          <span class="square bottom left"></span>
          <p class="tips">将二维码放入框内，即可自动扫描</p>
        </div>
        <video
          v-show="showPlay"
          class="source"
          //   ref="video"
          ref={video}
          width="400"
          height="400"
          controls
        ></video>
        <canvas v-show="!showPlay" ref={canvas} />
        <button v-show="showPlay">开始</button>
      </div>
    </div>
  );
};

export default defineComponent(Scanner);
